import { motion } from 'framer-motion'
import { ArrowLeft, Truck, Clock, MapPin, Package, Shield, CreditCard } from 'lucide-react'

const ShippingInfoPage = ({ onBackToHome }) => {
  const shippingOptions = [
    {
      name: 'Standard Shipping',
      price: '$5.99',
      time: '5-7 business days',
      description: 'Reliable delivery for most orders',
      icon: Truck,
      features: ['Free on orders over $100', 'Tracking included', 'Signature not required']
    },
    {
      name: 'Express Shipping',
      price: '$15.99',
      time: '2-3 business days',
      description: 'Faster delivery when you need it',
      icon: Clock,
      features: ['Priority handling', 'Tracking included', 'Signature required']
    },
    {
      name: 'Overnight Shipping',
      price: '$29.99',
      time: '1 business day',
      description: 'Next day delivery for urgent orders',
      icon: Package,
      features: ['Next day delivery', 'Priority handling', 'Signature required', 'Insurance included']
    }
  ]

  const policies = [
    {
      title: 'Processing Time',
      description: 'Orders are processed within 1-2 business days. Orders placed after 2 PM EST will be processed the next business day.',
      icon: Clock
    },
    {
      title: 'Shipping Areas',
      description: 'We ship to all 50 US states and 25+ international countries. Some restrictions may apply to certain products.',
      icon: MapPin
    },
    {
      title: 'Package Protection',
      description: 'All orders are insured against loss or damage during transit. We work with carriers to resolve any issues.',
      icon: Shield
    },
    {
      title: 'Payment & Billing',
      description: 'Shipping charges are calculated at checkout based on your location and selected shipping method.',
      icon: CreditCard
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.button
            onClick={onBackToHome}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-6"
            whileHover={{ x: -5 }}
          >
            <ArrowLeft className="h-5 w-5" />
            Back to Home
          </motion.button>
          
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Shipping Information</h1>
          <p className="text-lg text-gray-600">
            Everything you need to know about our shipping options and policies
          </p>
        </motion.div>

        {/* Shipping Options */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Shipping Options</h2>
          <div className="grid md:grid-cols-3 gap-6">
            {shippingOptions.map((option, index) => {
              const Icon = option.icon
              return (
                <motion.div
                  key={option.name}
                  className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                  whileHover={{ y: -5, shadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)' }}
                >
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
                      <Icon className="h-6 w-6 text-primary-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{option.name}</h3>
                      <p className="text-primary-600 font-medium">{option.price}</p>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-2">{option.description}</p>
                  <p className="text-sm font-medium text-gray-900 mb-4">{option.time}</p>
                  
                  <ul className="space-y-2">
                    {option.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center gap-2 text-sm text-gray-600">
                        <div className="w-1.5 h-1.5 bg-primary-500 rounded-full"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </motion.div>
              )
            })}
          </div>
        </motion.div>

        {/* Policies */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Shipping Policies</h2>
          <div className="grid md:grid-cols-2 gap-6">
            {policies.map((policy, index) => {
              const Icon = policy.icon
              return (
                <motion.div
                  key={policy.title}
                  className="bg-white rounded-xl p-6 shadow-sm border border-gray-200"
                  initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                >
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Icon className="h-5 w-5 text-primary-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">{policy.title}</h3>
                      <p className="text-gray-600 text-sm">{policy.description}</p>
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </motion.div>

        {/* International Shipping */}
        <motion.div
          className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-4">International Shipping</h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Available Countries</h3>
              <p className="text-gray-600 mb-4">
                We currently ship to 25+ countries worldwide including Canada, UK, Australia, 
                Germany, France, Japan, and more.
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Delivery time: 7-14 business days</li>
                <li>• Customs fees may apply</li>
                <li>• Tracking included on all orders</li>
                <li>• Minimum order value may apply</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Important Notes</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Customs duties and taxes are the responsibility of the recipient</li>
                <li>• Some products may have shipping restrictions</li>
                <li>• International orders cannot be expedited</li>
                <li>• Address accuracy is crucial for international delivery</li>
              </ul>
            </div>
          </div>
        </motion.div>

        {/* Contact Section */}
        <motion.div
          className="mt-12 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Need Help with Shipping?</h3>
          <p className="text-gray-600 mb-6">
            Our customer service team is here to help with any shipping questions or concerns.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.button
              className="btn-primary"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Contact Support
            </motion.button>
            <motion.button
              className="btn-secondary"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Track Your Order
            </motion.button>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default ShippingInfoPage
