import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CheckCircle, X, ShoppingCart, Heart } from 'lucide-react'

const Notification = ({ notifications, onRemove }) => {
  return (
    <div className="fixed top-20 right-4 z-50 space-y-2">
      <AnimatePresence>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, x: 300, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 300, scale: 0.8 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="glass-card rounded-xl p-4 max-w-sm shadow-lg"
          >
            <div className="flex items-start gap-3">
              {/* Icon */}
              <div className={`flex-shrink-0 p-2 rounded-full ${
                notification.type === 'cart' ? 'bg-green-100' :
                notification.type === 'wishlist' ? 'bg-pink-100' : 'bg-blue-100'
              }`}>
                {notification.type === 'cart' && <ShoppingCart className="h-5 w-5 text-green-600" />}
                {notification.type === 'wishlist' && <Heart className="h-5 w-5 text-pink-600" />}
                {notification.type === 'success' && <CheckCircle className="h-5 w-5 text-green-600" />}
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900">
                  {notification.title}
                </h4>
                <p className="text-sm text-gray-600 mt-1">
                  {notification.message}
                </p>
                {notification.product && (
                  <div className="flex items-center gap-2 mt-2">
                    <img
                      src={notification.product.image}
                      alt={notification.product.name}
                      className="w-8 h-8 rounded object-cover"
                    />
                    <span className="text-xs text-gray-500 truncate">
                      {notification.product.name}
                    </span>
                  </div>
                )}
              </div>

              {/* Close Button */}
              <motion.button
                onClick={() => onRemove(notification.id)}
                className="flex-shrink-0 p-1 rounded-full hover:bg-gray-100 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <X className="h-4 w-4 text-gray-400" />
              </motion.button>
            </div>

            {/* Progress Bar */}
            <motion.div
              className="mt-3 h-1 bg-gray-200 rounded-full overflow-hidden"
              initial={{ width: "100%" }}
              animate={{ width: "0%" }}
              transition={{ duration: notification.duration || 4, ease: "linear" }}
            >
              <div className={`h-full ${
                notification.type === 'cart' ? 'bg-green-500' :
                notification.type === 'wishlist' ? 'bg-pink-500' : 'bg-blue-500'
              }`} />
            </motion.div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  )
}

// Hook for managing notifications
export const useNotifications = () => {
  const [notifications, setNotifications] = useState([])
  const [recentNotifications, setRecentNotifications] = useState(new Set())
  const lastNotificationRef = useRef({ key: null, timestamp: 0 })

  const addNotification = (notification) => {
    const now = Date.now()
    const duplicateKey = `${notification.type}-${notification.product?.id}-${notification.message}`
    
    // Check for immediate duplicates (within 50ms - likely StrictMode)
    if (lastNotificationRef.current.key === duplicateKey && 
        (now - lastNotificationRef.current.timestamp) < 50) {
      console.log('Immediate duplicate notification prevented (StrictMode):', duplicateKey)
      return
    }
    
    // Check if we've already shown this notification recently (within 1 second)
    if (recentNotifications.has(duplicateKey)) {
      console.log('Recent duplicate notification prevented:', duplicateKey)
      return
    }

    // Update ref to track this notification
    lastNotificationRef.current = { key: duplicateKey, timestamp: now }

    // Add to recent notifications to prevent duplicates
    setRecentNotifications(prev => new Set([...prev, duplicateKey]))

    // Remove from recent notifications after 1 second
    setTimeout(() => {
      setRecentNotifications(prev => {
        const newSet = new Set(prev)
        newSet.delete(duplicateKey)
        return newSet
      })
    }, 1000)

    const id = Date.now() + Math.random()
    const newNotification = {
      id,
      duration: 4000,
      ...notification
    }

    setNotifications(prev => [...prev, newNotification])

    // Auto remove after duration
    setTimeout(() => {
      removeNotification(id)
    }, newNotification.duration)
  }

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const notifyCartAdd = (product, quantity = 1) => {
    addNotification({
      type: 'cart',
      title: 'Added to Cart',
      message: `${quantity} item${quantity > 1 ? 's' : ''} added to your cart`,
      product
    })
  }

  const notifyWishlistAdd = (product) => {
    addNotification({
      type: 'wishlist',
      title: 'Added to Wishlist',
      message: 'Item saved to your wishlist',
      product
    })
  }

  const notifyWishlistRemove = (product) => {
    addNotification({
      type: 'wishlist',
      title: 'Removed from Wishlist',
      message: 'Item removed from your wishlist',
      product
    })
  }

  const notifySuccess = (title, message) => {
    addNotification({
      type: 'success',
      title,
      message
    })
  }

  return {
    notifications,
    removeNotification,
    notifyCartAdd,
    notifyWishlistAdd,
    notifyWishlistRemove,
    notifySuccess
  }
}

export default Notification
