import { motion } from 'framer-motion'
import { ArrowLeft, FileText, Shield, CreditCard, Truck, AlertTriangle, Scale } from 'lucide-react'

const TermsOfServicePage = ({ onBackToHome }) => {
  const sections = [
    {
      title: 'Acceptance of Terms',
      icon: FileText,
      content: [
        'By accessing and using EliteShop, you accept and agree to be bound by these Terms of Service',
        'If you do not agree to these terms, please do not use our website or services',
        'We reserve the right to modify these terms at any time with notice',
        'Continued use of our service constitutes acceptance of any changes'
      ]
    },
    {
      title: 'Account Registration',
      icon: Shield,
      content: [
        'You must provide accurate and complete information when creating an account',
        'You are responsible for maintaining the confidentiality of your account credentials',
        'You must notify us immediately of any unauthorized use of your account',
        'We reserve the right to suspend or terminate accounts that violate these terms'
      ]
    },
    {
      title: 'Orders and Payment',
      icon: CreditCard,
      content: [
        'All orders are subject to acceptance and availability',
        'Prices are subject to change without notice until order confirmation',
        'Payment must be received before order processing begins',
        'We accept major credit cards and other payment methods as displayed',
        'You authorize us to charge your payment method for all purchases'
      ]
    },
    {
      title: 'Shipping and Delivery',
      icon: Truck,
      content: [
        'Delivery times are estimates and not guaranteed',
        'Risk of loss passes to you upon delivery to the carrier',
        'We are not responsible for delays caused by shipping carriers',
        'International orders may be subject to customs duties and taxes'
      ]
    },
    {
      title: 'Returns and Refunds',
      icon: AlertTriangle,
      content: [
        'Returns must be initiated within 30 days of delivery',
        'Items must be in original condition with all packaging and tags',
        'Certain items are not eligible for return as specified in our return policy',
        'Refunds will be processed to the original payment method',
        'Return shipping costs may apply unless the item was defective'
      ]
    },
    {
      title: 'Intellectual Property',
      icon: Scale,
      content: [
        'All content on this website is owned by EliteShop or our licensors',
        'You may not reproduce, distribute, or create derivative works without permission',
        'Product images and descriptions are for reference only',
        'Trademarks and logos are the property of their respective owners'
      ]
    }
  ]

  const prohibitedUses = [
    'Using the service for any unlawful purpose',
    'Attempting to gain unauthorized access to our systems',
    'Interfering with the proper functioning of the website',
    'Uploading malicious code or viruses',
    'Impersonating another person or entity',
    'Collecting user information without consent',
    'Engaging in fraudulent activities'
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.button
            onClick={onBackToHome}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-6"
            whileHover={{ x: -5 }}
          >
            <ArrowLeft className="h-5 w-5" />
            Back to Home
          </motion.button>
          
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Terms of Service</h1>
          <p className="text-lg text-gray-600 mb-4">
            Please read these terms carefully before using our services.
          </p>
          <p className="text-sm text-gray-500">
            Last updated: January 2024
          </p>
        </motion.div>

        {/* Introduction */}
        <motion.div
          className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Agreement Overview</h2>
          <p className="text-gray-600 mb-4">
            These Terms of Service ("Terms") govern your use of the EliteShop website and services. 
            By using our website, you agree to comply with and be bound by these terms.
          </p>
          <p className="text-gray-600">
            These terms constitute a legally binding agreement between you and EliteShop. 
            Please read them carefully and contact us if you have any questions.
          </p>
        </motion.div>

        {/* Terms Sections */}
        <div className="space-y-8">
          {sections.map((section, index) => {
            const Icon = section.icon
            return (
              <motion.div
                key={section.title}
                className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
              >
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
                    <Icon className="h-6 w-6 text-primary-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">{section.title}</h2>
                </div>
                
                <ul className="space-y-3">
                  {section.content.map((item, idx) => (
                    <li key={idx} className="flex items-start gap-3 text-gray-600">
                      <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            )
          })}
        </div>

        {/* Prohibited Uses */}
        <motion.div
          className="bg-red-50 border border-red-200 rounded-2xl p-8 mt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <div className="flex items-center gap-4 mb-6">
            <AlertTriangle className="h-8 w-8 text-red-600" />
            <h2 className="text-2xl font-bold text-red-900">Prohibited Uses</h2>
          </div>
          
          <p className="text-red-800 mb-4">
            You may not use our service for any of the following purposes:
          </p>
          
          <ul className="space-y-2">
            {prohibitedUses.map((use, index) => (
              <li key={index} className="flex items-start gap-3 text-red-700">
                <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                <span>{use}</span>
              </li>
            ))}
          </ul>
        </motion.div>

        {/* Limitation of Liability */}
        <motion.div
          className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 mt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Limitation of Liability</h2>
          <div className="space-y-4 text-gray-600">
            <p>
              EliteShop shall not be liable for any indirect, incidental, special, consequential, 
              or punitive damages resulting from your use of our service.
            </p>
            <p>
              Our total liability to you for any damages shall not exceed the amount you paid 
              for products or services in the 12 months preceding the claim.
            </p>
            <p>
              Some jurisdictions do not allow the exclusion of certain warranties or the limitation 
              of liability for consequential damages, so some of the above limitations may not apply to you.
            </p>
          </div>
        </motion.div>

        {/* Contact Section */}
        <motion.div
          className="bg-primary-50 rounded-2xl p-8 mt-8 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.2 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Questions About These Terms?</h2>
          <p className="text-gray-600 mb-6">
            If you have any questions about these Terms of Service, please contact our legal team.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.button
              className="btn-primary"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Contact Legal Team
            </motion.button>
            <motion.button
              className="btn-secondary"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Download PDF
            </motion.button>
          </div>
          
          <div className="mt-6 pt-6 border-t border-primary-200">
            <p className="text-sm text-gray-500">
              Email: <EMAIL> | Phone: +****************
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default TermsOfServicePage
