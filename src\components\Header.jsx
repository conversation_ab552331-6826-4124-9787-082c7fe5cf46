import { useState } from 'react'
import { motion } from 'framer-motion'
import { Search, ShoppingCart, User, Heart, Menu, X } from 'lucide-react'
import SearchResults from './SearchResults'
import ProfileDropdown from './ProfileDropdown'

const Header = ({ cartItems = [], onAddToCart, onToggleWishlist, onToggleCart, wishlist = new Set(), user, onLogin, onLogout, onRegister, onWishlistClick }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isSearchFocused, setIsSearchFocused] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [showSearchResults, setShowSearchResults] = useState(false)
  const [isProfileOpen, setIsProfileOpen] = useState(false)

  const cartItemsCount = cartItems.reduce((total, item) => total + item.quantity, 0)

  const handleSearch = (e) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      setShowSearchResults(true)
    }
  }

  const handleSearchInputChange = (e) => {
    setSearchQuery(e.target.value)
  }

  const handleCartClick = () => {
    if (onToggleCart) {
      onToggleCart()
    }
  }

  const handleProfileClick = () => {
    setIsProfileOpen(!isProfileOpen)
  }

  const handleWishlistClick = () => {
    if (onWishlistClick) {
      onWishlistClick()
    }
  }

  return (
    <motion.header 
      className="sticky top-0 z-50 glass-effect"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <motion.div 
            className="flex-shrink-0"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <h1 className="text-2xl font-bold text-gradient">
              EliteShop
            </h1>
          </motion.div>

          {/* Search Bar - Desktop */}
          <div className="hidden md:flex flex-1 max-w-lg mx-8">
            <form onSubmit={handleSearch} className={`relative w-full transition-all duration-300 ${
              isSearchFocused ? 'scale-105' : ''
            }`}>
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-500" />
              </div>
              <input
                type="text"
                placeholder="Search for products..."
                value={searchQuery}
                onChange={handleSearchInputChange}
                className="w-full pl-10 pr-4 py-2 glass-card rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                onFocus={() => setIsSearchFocused(true)}
                onBlur={() => setIsSearchFocused(false)}
              />
            </form>
          </div>

          {/* Navigation Icons - Desktop */}
          <div className="hidden md:flex items-center space-x-4">
            <motion.button
              onClick={handleWishlistClick}
              className="p-2 rounded-xl glass-card hover:bg-white/90 transition-all duration-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Heart className="h-5 w-5 text-gray-600" />
            </motion.button>

            <div className="relative">
              <motion.button
                onClick={handleProfileClick}
                className="p-2 rounded-xl glass-card hover:bg-white/90 transition-all duration-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                data-profile-button
              >
                <User className="h-5 w-5 text-gray-600" />
              </motion.button>

              <ProfileDropdown
                isOpen={isProfileOpen}
                onClose={() => setIsProfileOpen(false)}
                user={user}
                onLogin={onLogin}
                onLogout={onLogout}
                onRegister={onRegister}
              />
            </div>

            <motion.button
              onClick={handleCartClick}
              className="relative p-2 rounded-xl glass-card hover:bg-white/90 transition-all duration-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <ShoppingCart className="h-5 w-5 text-gray-600" />
              {cartItemsCount > 0 && (
                <motion.span
                  className="absolute -top-1 -right-1 bg-gradient-to-r from-accent-coral to-accent-lime text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                >
                  {cartItemsCount}
                </motion.span>
              )}
            </motion.button>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <motion.button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 rounded-xl glass-card"
              whileTap={{ scale: 0.9 }}
            >
              {isMenuOpen ? (
                <X className="h-6 w-6 text-gray-600" />
              ) : (
                <Menu className="h-6 w-6 text-gray-600" />
              )}
            </motion.button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <motion.div
            className="md:hidden py-4 border-t border-white/20"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            {/* Mobile Search */}
            <div className="mb-4">
              <form onSubmit={handleSearch} className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-500" />
                </div>
                <input
                  type="text"
                  placeholder="Search for products..."
                  value={searchQuery}
                  onChange={handleSearchInputChange}
                  className="w-full pl-10 pr-4 py-2 glass-card rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </form>
            </div>

            {/* Mobile Navigation */}
            <div className="flex justify-around">
              <motion.button
                onClick={handleWishlistClick}
                className="flex flex-col items-center p-2"
                whileTap={{ scale: 0.9 }}
              >
                <Heart className="h-6 w-6 text-gray-600 mb-1" />
                <span className="text-xs text-gray-600">Wishlist</span>
              </motion.button>

              <motion.button
                onClick={handleProfileClick}
                className="flex flex-col items-center p-2"
                whileTap={{ scale: 0.9 }}
              >
                <User className="h-6 w-6 text-gray-600 mb-1" />
                <span className="text-xs text-gray-600">Profile</span>
              </motion.button>

              <motion.button
                onClick={handleCartClick}
                className="flex flex-col items-center p-2 relative"
                whileTap={{ scale: 0.9 }}
              >
                <ShoppingCart className="h-6 w-6 text-gray-600 mb-1" />
                <span className="text-xs text-gray-600">Cart</span>
                {cartItemsCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-gradient-to-r from-accent-coral to-accent-lime text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                    {cartItemsCount}
                  </span>
                )}
              </motion.button>
            </div>
          </motion.div>
        )}
      </div>

      {/* Search Results Modal */}
      {showSearchResults && (
        <SearchResults
          searchQuery={searchQuery}
          onClose={() => setShowSearchResults(false)}
          onAddToCart={onAddToCart}
          onToggleWishlist={onToggleWishlist}
          wishlist={wishlist}
        />
      )}
    </motion.header>
  )
}

export default Header
