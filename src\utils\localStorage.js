// localStorage utilities for data persistence

const STORAGE_KEYS = {
  CART_ITEMS: 'eliteshop_cart_items',
  WISHLIST: 'eliteshop_wishlist',
  USER: 'eliteshop_user',
  CHECKOUT_FORM: 'eliteshop_checkout_form'
}

// Generic localStorage functions
export const saveToStorage = (key, data) => {
  try {
    const serializedData = JSON.stringify(data)
    localStorage.setItem(key, serializedData)
  } catch (error) {
    console.error('Error saving to localStorage:', error)
  }
}

export const loadFromStorage = (key, defaultValue = null) => {
  try {
    const serializedData = localStorage.getItem(key)
    if (serializedData === null) {
      return defaultValue
    }
    return JSON.parse(serializedData)
  } catch (error) {
    console.error('Error loading from localStorage:', error)
    return defaultValue
  }
}

export const removeFromStorage = (key) => {
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.error('Error removing from localStorage:', error)
  }
}

// Specific data persistence functions
export const saveCartItems = (cartItems) => {
  saveToStorage(STORAGE_KEYS.CART_ITEMS, cartItems)
}

export const loadCartItems = () => {
  return loadFromStorage(STORAGE_KEYS.CART_ITEMS, [])
}

export const saveWishlist = (wishlist) => {
  // Convert Set to Array for storage
  const wishlistArray = Array.from(wishlist)
  saveToStorage(STORAGE_KEYS.WISHLIST, wishlistArray)
}

export const loadWishlist = () => {
  const wishlistArray = loadFromStorage(STORAGE_KEYS.WISHLIST, [])
  // Convert Array back to Set
  return new Set(wishlistArray)
}

export const saveUser = (user) => {
  saveToStorage(STORAGE_KEYS.USER, user)
}

export const loadUser = () => {
  return loadFromStorage(STORAGE_KEYS.USER, null)
}

export const saveCheckoutForm = (formData) => {
  // Don't save sensitive payment information
  const safeFormData = {
    firstName: formData.firstName,
    lastName: formData.lastName,
    email: formData.email,
    phone: formData.phone,
    address: formData.address,
    city: formData.city,
    state: formData.state,
    zipCode: formData.zipCode,
    country: formData.country,
    shippingMethod: formData.shippingMethod
    // Exclude payment information for security
  }
  saveToStorage(STORAGE_KEYS.CHECKOUT_FORM, safeFormData)
}

export const loadCheckoutForm = () => {
  return loadFromStorage(STORAGE_KEYS.CHECKOUT_FORM, {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'United States',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardName: '',
    shippingMethod: 'standard'
  })
}

export const clearCheckoutForm = () => {
  removeFromStorage(STORAGE_KEYS.CHECKOUT_FORM)
}

export const clearAllData = () => {
  Object.values(STORAGE_KEYS).forEach(key => {
    removeFromStorage(key)
  })
}

// Utility to check if localStorage is available
export const isStorageAvailable = () => {
  try {
    const test = '__storage_test__'
    localStorage.setItem(test, test)
    localStorage.removeItem(test)
    return true
  } catch (error) {
    return false
  }
}
