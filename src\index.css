@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply antialiased;
    background-color: #fafbfc;
    color: #222222;
    font-family: 'Inter', 'Montserrat', system-ui, sans-serif;
    margin: 0;
    min-height: 100vh;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .glass-effect {
    @apply bg-white/20 backdrop-blur-md border border-white/30;
  }

  .glass-card {
    @apply bg-white/80 backdrop-blur-sm border border-white/50 shadow-lg;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:shadow-lg hover:scale-105 active:scale-95;
    box-shadow: 0 4px 14px 0 rgba(15, 240, 252, 0.25);
  }

  .btn-secondary {
    @apply bg-white/90 backdrop-blur-sm px-6 py-3 rounded-xl font-medium border border-white/50 transition-all duration-300 hover:bg-white hover:shadow-lg hover:scale-105 active:scale-95;
    color: #222222;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-xl hover:shadow-black/10 hover:-translate-y-2;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent;
  }

  .floating-action {
    @apply fixed bottom-6 right-6 z-50 bg-gradient-to-r from-primary-500 to-secondary-500 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110;
  }
}
