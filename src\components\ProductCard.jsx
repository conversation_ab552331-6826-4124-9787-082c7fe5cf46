import { useState } from 'react'
import { motion } from 'framer-motion'
import { Heart, ShoppingCart, Star, Eye, Plus, Minus } from 'lucide-react'

const ProductCard = ({
  product,
  onAddToCart,
  onToggleWishlist,
  onProductClick,
  onQuickView,
  isWishlisted = false,
  showQuickAdd = true,
  className = "",
  viewMode = "grid" // "grid" or "list"
}) => {
  const [quantity, setQuantity] = useState(1)
  const [isHovered, setIsHovered] = useState(false)

  const handleAddToCart = () => {
    onAddToCart({ ...product, quantity })
  }

  const handleQuantityChange = (change) => {
    const newQuantity = Math.max(1, Math.min(product.stock || 99, quantity + change))
    setQuantity(newQuantity)
  }

  const discountPercentage = product.originalPrice 
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0

  if (viewMode === 'list') {
    return (
      <motion.div
        className={`group cursor-pointer ${className}`}
        whileHover={{ x: 8 }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        onClick={() => onProductClick?.(product)}
      >
        <div className="glass-card rounded-2xl overflow-hidden card-hover h-full flex flex-row">
          {/* Product Image - List View */}
          <div className="relative overflow-hidden flex-shrink-0 w-48">
            <img
              src={product.image}
              alt={product.name}
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            />

            {/* Badges */}
            <div className="absolute top-4 left-4 flex flex-col gap-2">
              {product.badge && (
                <div className={`px-3 py-1 ${product.badgeColor} text-white text-xs font-medium rounded-full`}>
                  {product.badge}
                </div>
              )}
              {discountPercentage > 0 && (
                <div className="px-3 py-1 bg-red-500 text-white text-xs font-medium rounded-full">
                  -{discountPercentage}%
                </div>
              )}
            </div>

            {/* Wishlist Button */}
            <motion.button
              onClick={(e) => {
                e.stopPropagation();
                onToggleWishlist?.(product.id);
              }}
              className={`absolute top-4 right-4 p-2 rounded-full backdrop-blur-sm transition-all duration-300 ${
                isWishlisted
                  ? 'bg-accent-coral text-white'
                  : 'bg-white/20 text-white hover:bg-white/30'
              }`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Heart className={`h-4 w-4 ${isWishlisted ? 'fill-current' : ''}`} />
            </motion.button>
          </div>

          {/* Product Info - List View */}
          <div className="relative z-20 p-6 flex-1 flex flex-col justify-between">
            <div>
              <h3 className="text-xl font-semibold mb-2 group-hover:text-gradient transition-all duration-300" style={{ color: '#222222' }}>
                {product.name}
              </h3>

              <p className="text-sm text-primary-600 mb-2 capitalize font-medium">
                {product.category?.replace('-', ' & ')}
              </p>

              {/* Rating */}
              <div className="flex items-center gap-2 mb-3">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${
                        i < Math.floor(product.rating)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm" style={{ color: '#43464e' }}>
                  {product.rating} ({product.reviews})
                </span>
              </div>

              {/* Description */}
              {product.description && (
                <p className="text-sm mb-4" style={{ color: '#43464e' }}>
                  {product.description.length > 120
                    ? `${product.description.substring(0, 120)}...`
                    : product.description}
                </p>
              )}
            </div>

            <div className="flex items-center justify-between">
              {/* Price */}
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold" style={{ color: '#222222' }}>
                  ${product.price}
                </span>
                {product.originalPrice && (
                  <span className="text-lg line-through" style={{ color: '#43464e' }}>
                    ${product.originalPrice}
                  </span>
                )}
              </div>

              {/* Add to Cart Button */}
              {product.inStock ? (
                <motion.button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAddToCart();
                  }}
                  className="relative z-30 bg-gradient-to-r from-primary-500 to-secondary-500 text-white py-2 px-6 rounded-xl font-medium transition-all duration-300 hover:shadow-lg flex items-center gap-2"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <ShoppingCart className="h-4 w-4" />
                  Add to Cart
                </motion.button>
              ) : (
                <span className="text-red-500 font-medium">Out of Stock</span>
              )}
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      className={`group cursor-pointer ${className}`}
      whileHover={{ y: -8 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      onClick={() => onProductClick?.(product)}
    >
      <div className="glass-card rounded-2xl overflow-hidden card-hover h-full flex flex-col">
        {/* Product Image */}
        <div className="relative overflow-hidden flex-shrink-0">
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
          />
          
          {/* Badges */}
          <div className="absolute top-4 left-4 flex flex-col gap-2">
            {product.badge && (
              <div className={`px-3 py-1 ${product.badgeColor} text-white text-xs font-medium rounded-full`}>
                {product.badge}
              </div>
            )}
            {discountPercentage > 0 && (
              <div className="px-3 py-1 bg-red-500 text-white text-xs font-medium rounded-full">
                -{discountPercentage}%
              </div>
            )}
            {!product.inStock && (
              <div className="px-3 py-1 bg-gray-500 text-white text-xs font-medium rounded-full">
                Out of Stock
              </div>
            )}
          </div>

          {/* Wishlist Button */}
          <motion.button
            onClick={(e) => {
              e.stopPropagation();
              onToggleWishlist?.(product.id);
            }}
            className={`absolute top-4 right-4 p-2 rounded-full backdrop-blur-sm transition-all duration-300 ${
              isWishlisted
                ? 'bg-accent-coral text-white'
                : 'bg-white/20 text-white hover:bg-white/30'
            }`}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Heart className={`h-4 w-4 ${isWishlisted ? 'fill-current' : ''}`} />
          </motion.button>

          {/* Quick Actions Overlay */}
          {showQuickAdd && (
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
              <div className="flex gap-3">
                <motion.button
                  onClick={(e) => {
                    e.stopPropagation();
                    onQuickView?.(product);
                  }}
                  className="p-3 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  title="Quick View"
                >
                  <Eye className="h-5 w-5" />
                </motion.button>
                {product.inStock && (
                  <motion.button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart();
                    }}
                    className="p-3 bg-primary-500 rounded-full text-white hover:bg-primary-600 transition-colors"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    title="Add to Cart"
                  >
                    <ShoppingCart className="h-5 w-5" />
                  </motion.button>
                )}
              </div>
            </div>
          )}

          {/* Stock Indicator */}
          {product.stock && product.stock <= 10 && product.inStock && (
            <div className="absolute bottom-4 left-4 px-2 py-1 bg-orange-500 text-white text-xs rounded-full">
              Only {product.stock} left!
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="relative z-20 p-6 flex-1 flex flex-col">
          <h3 className="text-lg font-semibold mb-2 group-hover:text-gradient transition-all duration-300" style={{ color: '#222222' }}>
            {product.name}
          </h3>

          {/* Category */}
          <p className="text-sm text-primary-600 mb-2 capitalize font-medium">
            {product.category?.replace('-', ' & ')}
          </p>

          {/* Rating */}
          <div className="flex items-center gap-2 mb-3">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`h-4 w-4 ${
                    i < Math.floor(product.rating)
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <span className="text-sm" style={{ color: '#43464e' }}>
              {product.rating} ({product.reviews})
            </span>
          </div>

          {/* Price */}
          <div className="flex items-center gap-2 mb-4">
            <span className="text-2xl font-bold" style={{ color: '#222222' }}>
              ${product.price}
            </span>
            {product.originalPrice && (
              <span className="text-lg line-through" style={{ color: '#43464e' }}>
                ${product.originalPrice}
              </span>
            )}
          </div>

          {/* Quantity Selector & Add to Cart */}
          <div className="mt-auto">
            {product.inStock ? (
              <div className="space-y-3">
                {/* Quantity Selector */}
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium" style={{ color: '#222222' }}>Quantity:</span>
                  <div className="flex items-center gap-2">
                    <motion.button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleQuantityChange(-1);
                      }}
                      className="p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      disabled={quantity <= 1}
                    >
                      <Minus className="h-4 w-4 text-gray-600" />
                    </motion.button>
                    <span className="w-8 text-center font-medium" style={{ color: '#222222' }}>
                      {quantity}
                    </span>
                    <motion.button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleQuantityChange(1);
                      }}
                      className="p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      disabled={quantity >= (product.stock || 99)}
                    >
                      <Plus className="h-4 w-4 text-gray-600" />
                    </motion.button>
                  </div>
                </div>

                {/* Add to Cart Button */}
                <motion.button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAddToCart();
                  }}
                  className="relative z-30 w-full bg-gradient-to-r from-primary-500 to-secondary-500 text-white py-3 px-4 rounded-xl font-medium transition-all duration-300 hover:shadow-lg hover:scale-105 active:scale-95 flex items-center justify-center gap-2"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <ShoppingCart className="h-4 w-4" />
                  Add to Cart
                </motion.button>
              </div>
            ) : (
              <div className="text-center py-3">
                <span className="text-red-500 font-medium">Out of Stock</span>
                <motion.button
                  className="w-full mt-2 bg-white/90 backdrop-blur-sm px-6 py-3 rounded-xl font-medium border border-white/50 transition-all duration-300 hover:bg-white hover:shadow-lg hover:scale-105 active:scale-95"
                  style={{ color: '#222222' }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Notify When Available
                </motion.button>
              </div>
            )}
          </div>

          {/* Features */}
          {product.features && product.features.length > 0 && isHovered && (
            <motion.div
              className="mt-4 pt-4 border-t border-gray-200"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
            >
              <h4 className="text-sm font-medium mb-2" style={{ color: '#222222' }}>Key Features:</h4>
              <ul className="text-xs space-y-1" style={{ color: '#43464e' }}>
                {product.features.slice(0, 3).map((feature, index) => (
                  <li key={index} className="flex items-center gap-1">
                    <div className="w-1 h-1 bg-primary-500 rounded-full"></div>
                    {feature}
                  </li>
                ))}
              </ul>
            </motion.div>
          )}
        </div>
      </div>
    </motion.div>
  )
}

export default ProductCard
