{"name": "e-commerce", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.7", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.534.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^3.4.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4", "vite-plugin-pwa": "^1.0.2"}}