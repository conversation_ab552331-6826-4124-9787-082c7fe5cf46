import { useState, useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { ArrowLeft } from 'lucide-react'
import Header from './components/Header'
import Hero from './components/Hero'
import FeaturedCategories from './components/FeaturedCategories'
import TrendingProducts from './components/TrendingProducts'
import PersonalizedRecommendations from './components/PersonalizedRecommendations'
import Footer from './components/Footer'
import FloatingCart from './components/FloatingCart'
import CartSidebar from './components/CartSidebar'
import CategoryPage from './components/CategoryPage'
import WishlistPage from './components/WishlistPage'
import CheckoutPage from './components/CheckoutPage'
import QuickViewModal from './components/QuickViewModal'
import ShippingInfoPage from './components/ShippingInfoPage'
import ReturnsPage from './components/ReturnsPage'
import SizeGuidePage from './components/SizeGuidePage'
import PrivacyPolicyPage from './components/PrivacyPolicyPage'
import TermsOfServicePage from './components/TermsOfServicePage'
import CareersPage from './components/CareersPage'
import CookiePolicyPage from './components/CookiePolicyPage'
import AllCategoriesPage from './components/AllCategoriesPage'
import AllProductsPage from './components/AllProductsPage'
import PWAInstallPrompt from './components/PWAInstallPrompt'
import Notification, { useNotifications } from './components/Notification'
import { getProductById, getProductsByCategory, categories, getCategoriesWithCounts } from './data/products'
import {
  saveCartItems,
  loadCartItems,
  saveWishlist,
  loadWishlist,
  saveUser,
  loadUser,
  isStorageAvailable
} from './utils/localStorage'

function App() {
  // Initialize state with data from localStorage
  const [cartItems, setCartItems] = useState(() => {
    return isStorageAvailable() ? loadCartItems() : []
  })
  const [wishlist, setWishlist] = useState(() => {
    return isStorageAvailable() ? loadWishlist() : new Set()
  })
  const [isCartOpen, setIsCartOpen] = useState(false)
  const [currentView, setCurrentView] = useState('home') // 'home', 'category', 'wishlist', 'checkout', 'shipping', 'returns', etc.
  const [selectedCategory, setSelectedCategory] = useState(null)
  const [filteredProducts, setFilteredProducts] = useState([])
  const [user, setUser] = useState(() => {
    return isStorageAvailable() ? loadUser() : null
  }) // User authentication state
  const [quickViewProduct, setQuickViewProduct] = useState(null)
  const [isQuickViewOpen, setIsQuickViewOpen] = useState(false)
  const {
    notifications,
    removeNotification,
    notifyCartAdd,
    notifyWishlistAdd,
    notifyWishlistRemove
  } = useNotifications()

  // Enhanced navigation functionality with browser history support
  useEffect(() => {
    const handleMouseNavigation = (e) => {
      // Check if we're on a page that supports back navigation
      if (currentView !== 'home') {
        // Mouse button 3 (back button) - most common
        // Mouse button 4 (forward button) - less common but supported
        if (e.button === 3 || e.button === 4) {
          e.preventDefault()
          handleViewAllProducts() // Go back to home
        }
      }
    }

    const handleKeyNavigation = (e) => {
      // Don't interfere if user is typing in an input field
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
        return
      }

      // Multiple keyboard shortcuts for navigation
      if (currentView !== 'home') {
        // Alt + Left Arrow (standard browser back)
        if (e.altKey && e.key === 'ArrowLeft') {
          e.preventDefault()
          handleViewAllProducts()
        }
        // Backspace (when not in input field)
        else if (e.key === 'Backspace') {
          e.preventDefault()
          handleViewAllProducts()
        }
        // Escape key
        else if (e.key === 'Escape') {
          e.preventDefault()
          handleViewAllProducts()
        }
      }
    }

    const handlePopState = (e) => {
      // Handle browser back/forward buttons
      if (e.state) {
        setCurrentView(e.state.view || 'home')
        setSelectedCategory(e.state.category || null)
        setFilteredProducts(e.state.products || [])
      } else {
        handleViewAllProducts()
      }
    }

    // Add event listeners
    document.addEventListener('mouseup', handleMouseNavigation)
    document.addEventListener('keydown', handleKeyNavigation)
    window.addEventListener('popstate', handlePopState)

    // Cleanup
    return () => {
      document.removeEventListener('mouseup', handleMouseNavigation)
      document.removeEventListener('keydown', handleKeyNavigation)
      window.removeEventListener('popstate', handlePopState)
    }
  }, [currentView])

  // Update browser history when view changes
  useEffect(() => {
    const state = {
      view: currentView,
      category: selectedCategory,
      products: filteredProducts
    }

    if (currentView === 'home') {
      window.history.pushState(state, '', '/')
    } else if (currentView === 'category' && selectedCategory) {
      window.history.pushState(state, '', `/category/${selectedCategory.slug}`)
    } else if (currentView === 'all-categories') {
      window.history.pushState(state, '', '/categories')
    } else if (currentView === 'all-products') {
      window.history.pushState(state, '', '/products')
    } else if (currentView === 'wishlist') {
      window.history.pushState(state, '', '/wishlist')
    } else if (currentView === 'checkout') {
      window.history.pushState(state, '', '/checkout')
    }
  }, [currentView, selectedCategory])

  // Update document title based on current view
  useEffect(() => {
    const titles = {
      'home': 'EliteShop - Premium E-Commerce Experience',
      'category': selectedCategory ? `${selectedCategory.name} - EliteShop` : 'Category - EliteShop',
      'all-categories': 'All Categories - EliteShop',
      'all-products': 'All Products - EliteShop',
      'wishlist': 'My Wishlist - EliteShop',
      'checkout': 'Checkout - EliteShop',
      'shipping': 'Shipping Information - EliteShop',
      'returns': 'Returns & Refunds - EliteShop',
      'size-guide': 'Size Guide - EliteShop',
      'privacy-policy': 'Privacy Policy - EliteShop',
      'terms-of-service': 'Terms of Service - EliteShop',
      'cookie-policy': 'Cookie Policy - EliteShop',
      'careers': 'Careers - EliteShop',
      'press': 'Press & Media - EliteShop',
      'sustainability': 'Sustainability - EliteShop',
      'investors': 'Investor Relations - EliteShop',
      'accessibility': 'Accessibility - EliteShop'
    }

    document.title = titles[currentView] || 'EliteShop - Premium E-Commerce Experience'
  }, [currentView, selectedCategory])

  // Save data to localStorage whenever it changes
  useEffect(() => {
    if (isStorageAvailable()) {
      saveCartItems(cartItems)
    }
  }, [cartItems])

  useEffect(() => {
    if (isStorageAvailable()) {
      saveWishlist(wishlist)
    }
  }, [wishlist])

  useEffect(() => {
    if (isStorageAvailable()) {
      saveUser(user)
    }
  }, [user])

  const addToCart = (product) => {
    const quantity = product.quantity || 1
    setCartItems(prev => {
      const existing = prev.find(item => item.id === product.id)
      if (existing) {
        const newItems = prev.map(item =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + quantity }
            : item
        )
        return newItems
      }
      return [...prev, { ...product, quantity }]
    })
    // Single notification call outside of setState
    notifyCartAdd(product, quantity)
  }

  const removeFromCart = (productId) => {
    setCartItems(prev => prev.filter(item => item.id !== productId))
  }

  const updateQuantity = (productId, quantity) => {
    if (quantity <= 0) {
      removeFromCart(productId)
      return
    }
    setCartItems(prev =>
      prev.map(item =>
        item.id === productId
          ? { ...item, quantity }
          : item
      )
    )
  }

  // Use ref to prevent StrictMode double execution
  const lastWishlistAction = useRef({ productId: null, timestamp: 0, action: null })

  const toggleWishlist = (productId) => {
    const now = Date.now()
    const timeSinceLastAction = now - lastWishlistAction.current.timestamp
    
    // If same product was toggled within 100ms, ignore (StrictMode double execution)
    if (lastWishlistAction.current.productId === productId && timeSinceLastAction < 100) {
      console.log('Ignoring duplicate wishlist toggle due to StrictMode')
      return
    }

    console.log('toggleWishlist called for product:', productId)
    const product = getProductById(productId)
    
    setWishlist(prev => {
      const newWishlist = new Set(prev)
      const wasInWishlist = newWishlist.has(productId)
      
      if (wasInWishlist) {
        newWishlist.delete(productId)
        console.log('Removing from wishlist:', product?.name)
        
        // Update ref before notification
        lastWishlistAction.current = { productId, timestamp: now, action: 'remove' }
        
        if (product) notifyWishlistRemove(product)
      } else {
        newWishlist.add(productId)
        console.log('Adding to wishlist:', product?.name)
        
        // Update ref before notification
        lastWishlistAction.current = { productId, timestamp: now, action: 'add' }
        
        if (product) notifyWishlistAdd(product)
      }
      
      return newWishlist
    })
  }

  const handleProductClick = (product) => {
    // You can add navigation to product detail page here
  }

  const handleToggleCart = () => {
    setIsCartOpen(!isCartOpen)
  }

  const handleLogin = (loginData) => {
    // In a real app, you would validate credentials with a backend
    const mockUser = {
      id: 1,
      name: loginData.email.split('@')[0], // Use email prefix as name for demo
      email: loginData.email,
      avatar: null
    }
    setUser(mockUser)
  }

  const handleLogout = () => {
    setUser(null)
  }

  const handleRegister = (registerData) => {
    // In a real app, you would create account with a backend
    const mockUser = {
      id: Date.now(),
      name: registerData.name,
      email: registerData.email,
      avatar: null
    }
    setUser(mockUser)
  }

  const handleToggleWishlist = () => {
    setCurrentView('wishlist')
    // Scroll to top when changing views
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const handleQuickView = (product) => {
    setQuickViewProduct(product)
    setIsQuickViewOpen(true)
  }

  const handleCloseQuickView = () => {
    setIsQuickViewOpen(false)
    setQuickViewProduct(null)
  }

  const handleFooterNavigate = (destination) => {
    switch (destination) {
      case 'home':
        setCurrentView('home')
        setSelectedCategory(null)
        setFilteredProducts([])
        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' })
        break
      default:
        // Handle other navigation cases
    }
  }

  const handleCategoryClick = (category) => {
    if (category === 'all') {
      setCurrentView('all-categories')
      setSelectedCategory(null)
      setFilteredProducts([])
    } else {
      setCurrentView('category')
      setSelectedCategory(category)
      const products = getProductsByCategory(category.slug)
      setFilteredProducts(products)
    }
    // Scroll to top when changing views
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const handleViewAllProducts = () => {
    setCurrentView('all-products')
    setSelectedCategory(null)
    setFilteredProducts([])
    // Scroll to top when changing views
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const handleBackToHome = () => {
    setCurrentView('home')
    setSelectedCategory(null)
    setFilteredProducts([])
    // Scroll to top when changing views
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const handleShopNow = (slide) => {
    // Map slide titles to category slugs
    const categoryMap = {
      "Premium Electronics Store": "electronics",
      "Fashion & Lifestyle": "fashion",
      "Home & Living": "home-garden",
      "Sports & Fitness": "sports-fitness"
    }

    const categorySlug = categoryMap[slide.title]
    if (categorySlug) {
      // Find the category object
      const category = categories.find(cat => cat.slug === categorySlug)
      if (category) {
        handleCategoryClick(category)
      }
    }
  }

  const handleLearnMore = (slide) => {
    // For Learn More, we can show a modal with more information
    // or navigate to a specific info page
    // For now, let's scroll to the featured categories section
    const categoriesSection = document.getElementById('featured-categories')
    if (categoriesSection) {
      categoriesSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  }

  const handleCheckout = (items) => {
    setCurrentView('checkout')
    setIsCartOpen(false)
  }

  const handleOrderComplete = () => {
    setCartItems([])
    setCurrentView('home')
  }

  const handlePageNavigate = (page) => {
    setCurrentView(page)
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#fafbfc' }}>
      <Header
        cartItems={cartItems}
        onAddToCart={addToCart}
        onToggleWishlist={toggleWishlist}
        onToggleCart={handleToggleCart}
        onWishlistClick={handleToggleWishlist}
        wishlist={wishlist}
        user={user}
        onLogin={handleLogin}
        onLogout={handleLogout}
        onRegister={handleRegister}
      />

      <main>
        {currentView === 'home' ? (
          <>
            <Hero onShopNow={handleShopNow} onLearnMore={handleLearnMore} />

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <FeaturedCategories onCategoryClick={handleCategoryClick} />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <TrendingProducts
                onAddToCart={addToCart}
                onToggleWishlist={toggleWishlist}
                onProductClick={handleProductClick}
                onQuickView={handleQuickView}
                onViewAllProducts={handleViewAllProducts}
                wishlist={wishlist}
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <PersonalizedRecommendations
                onAddToCart={addToCart}
                onToggleWishlist={toggleWishlist}
                onProductClick={handleProductClick}
                onQuickView={handleQuickView}
                onRegister={handleRegister}
                wishlist={wishlist}
              />
            </motion.div>
          </>
        ) : currentView === 'category' ? (
          <CategoryPage
            category={selectedCategory}
            products={filteredProducts}
            onAddToCart={addToCart}
            onToggleWishlist={toggleWishlist}
            onProductClick={handleProductClick}
            onQuickView={handleQuickView}
            onBackToHome={handleViewAllProducts}
            wishlist={wishlist}
          />
        ) : currentView === 'all-categories' ? (
          <AllCategoriesPage
            onBackToHome={handleBackToHome}
            onCategoryClick={handleCategoryClick}
          />
        ) : currentView === 'all-products' ? (
          <AllProductsPage
            onBackToHome={handleBackToHome}
            onAddToCart={addToCart}
            onToggleWishlist={toggleWishlist}
            onProductClick={handleProductClick}
            onQuickView={handleQuickView}
            wishlist={wishlist}
          />
        ) : currentView === 'wishlist' ? (
          <WishlistPage
            wishlist={wishlist}
            onAddToCart={addToCart}
            onToggleWishlist={toggleWishlist}
            onProductClick={handleProductClick}
            onQuickView={handleQuickView}
            onBackToHome={handleViewAllProducts}
          />
        ) : currentView === 'checkout' ? (
          <CheckoutPage
            cartItems={cartItems}
            onBackToHome={handleViewAllProducts}
            onOrderComplete={handleOrderComplete}
          />
        ) : currentView === 'shipping' ? (
          <ShippingInfoPage onBackToHome={handleViewAllProducts} />
        ) : currentView === 'returns' ? (
          <ReturnsPage onBackToHome={handleViewAllProducts} />
        ) : currentView === 'size-guide' ? (
          <SizeGuidePage onBackToHome={handleViewAllProducts} />
        ) : currentView === 'privacy-policy' ? (
          <PrivacyPolicyPage onBackToHome={handleViewAllProducts} />
        ) : currentView === 'terms-of-service' ? (
          <TermsOfServicePage onBackToHome={handleViewAllProducts} />
        ) : currentView === 'careers' ? (
          <CareersPage onBackToHome={handleViewAllProducts} />
        ) : currentView === 'cookie-policy' ? (
          <CookiePolicyPage onBackToHome={handleViewAllProducts} />
        ) : currentView === 'press' ? (
          <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center">
              <button onClick={handleViewAllProducts} className="flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-6 mx-auto">
                <ArrowLeft className="h-5 w-5" />
                Back to Home
              </button>
              <h1 className="text-4xl font-bold text-gray-900 mb-4">Press & Media</h1>
              <p className="text-lg text-gray-600 mb-8">For press inquiries, please contact <NAME_EMAIL></p>
              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Media Kit</h2>
                <p className="text-gray-600 mb-6">Download our media kit for logos, product images, and company information.</p>
                <button className="btn-primary">Download Media Kit</button>
              </div>
            </div>
          </div>
        ) : currentView === 'sustainability' ? (
          <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center">
              <button onClick={handleViewAllProducts} className="flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-6 mx-auto">
                <ArrowLeft className="h-5 w-5" />
                Back to Home
              </button>
              <h1 className="text-4xl font-bold text-gray-900 mb-4">Sustainability</h1>
              <p className="text-lg text-gray-600 mb-8">We're committed to sustainable practices and environmental responsibility.</p>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">Eco-Friendly Packaging</h3>
                  <p className="text-gray-600">100% recyclable packaging materials and minimal waste practices.</p>
                </div>
                <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">Carbon Neutral Shipping</h3>
                  <p className="text-gray-600">We offset all shipping emissions through verified carbon credit programs.</p>
                </div>
              </div>
            </div>
          </div>
        ) : currentView === 'investors' ? (
          <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center">
              <button onClick={handleViewAllProducts} className="flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-6 mx-auto">
                <ArrowLeft className="h-5 w-5" />
                Back to Home
              </button>
              <h1 className="text-4xl font-bold text-gray-900 mb-4">Investor Relations</h1>
              <p className="text-lg text-gray-600 mb-8">Information for current and potential investors.</p>
              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Financial Information</h2>
                <p className="text-gray-600 mb-6">For investor inquiries and financial reports, please contact our investor relations team.</p>
                <button className="btn-primary">Contact Investor Relations</button>
              </div>
            </div>
          </div>
        ) : currentView === 'accessibility' ? (
          <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center">
              <button onClick={handleViewAllProducts} className="flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-6 mx-auto">
                <ArrowLeft className="h-5 w-5" />
                Back to Home
              </button>
              <h1 className="text-4xl font-bold text-gray-900 mb-4">Accessibility</h1>
              <p className="text-lg text-gray-600 mb-8">We're committed to making our website accessible to everyone.</p>
              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Our Commitment</h2>
                <p className="text-gray-600 mb-6">We strive to meet WCAG 2.1 AA standards and continuously improve accessibility.</p>
                <button className="btn-primary">Report Accessibility Issue</button>
              </div>
            </div>
          </div>
        ) : null}
      </main>

      <Footer onNavigate={handleFooterNavigate} onPageNavigate={handlePageNavigate} />
      <FloatingCart
        cartItems={cartItems}
        onUpdateQuantity={updateQuantity}
        onRemoveItem={removeFromCart}
        onCheckout={handleCheckout}
      />

      {/* Cart Sidebar */}
      <CartSidebar
        isOpen={isCartOpen}
        onClose={() => setIsCartOpen(false)}
        cartItems={cartItems}
        onUpdateQuantity={updateQuantity}
        onRemoveItem={removeFromCart}
        onCheckout={handleCheckout}
      />

      {/* Quick View Modal */}
      <QuickViewModal
        product={quickViewProduct}
        isOpen={isQuickViewOpen}
        onClose={handleCloseQuickView}
        onAddToCart={addToCart}
        onToggleWishlist={toggleWishlist}
        isWishlisted={quickViewProduct ? wishlist.has(quickViewProduct.id) : false}
      />

      {/* PWA Install Prompt */}
      <PWAInstallPrompt />

      {/* Notifications */}
      <Notification
        notifications={notifications}
        onRemove={removeNotification}
      />
    </div>
  )
}

export default App
