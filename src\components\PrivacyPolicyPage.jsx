import { motion } from 'framer-motion'
import { ArrowLeft, Shield, Eye, Lock, Users, Database, Globe } from 'lucide-react'

const PrivacyPolicyPage = ({ onBackToHome }) => {
  const sections = [
    {
      title: 'Information We Collect',
      icon: Database,
      content: [
        'Personal information you provide (name, email, address, phone)',
        'Payment information (processed securely by our payment partners)',
        'Account preferences and shopping history',
        'Device information and browsing behavior',
        'Location data (with your permission)',
        'Customer service interactions and feedback'
      ]
    },
    {
      title: 'How We Use Your Information',
      icon: Users,
      content: [
        'Process and fulfill your orders',
        'Provide customer support and service',
        'Send order confirmations and shipping updates',
        'Personalize your shopping experience',
        'Improve our products and services',
        'Send promotional emails (with your consent)',
        'Prevent fraud and ensure security'
      ]
    },
    {
      title: 'Information Sharing',
      icon: Globe,
      content: [
        'We never sell your personal information to third parties',
        'Shipping partners receive necessary delivery information',
        'Payment processors handle transaction data securely',
        'Analytics providers help us improve our service',
        'Legal authorities when required by law',
        'Business partners for joint promotions (with consent)'
      ]
    },
    {
      title: 'Data Security',
      icon: Lock,
      content: [
        'Industry-standard encryption for all data transmission',
        'Secure servers with regular security audits',
        'Limited access to personal information by employees',
        'Regular security training for all staff members',
        'Immediate notification of any security breaches',
        'Compliance with international security standards'
      ]
    },
    {
      title: 'Your Rights',
      icon: Eye,
      content: [
        'Access your personal information at any time',
        'Correct or update your information',
        'Delete your account and associated data',
        'Opt-out of marketing communications',
        'Request data portability',
        'File complaints with data protection authorities'
      ]
    },
    {
      title: 'Data Protection',
      icon: Shield,
      content: [
        'GDPR compliance for European customers',
        'CCPA compliance for California residents',
        'Regular privacy impact assessments',
        'Data minimization practices',
        'Automatic data deletion policies',
        'Privacy by design in all new features'
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.button
            onClick={onBackToHome}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-6"
            whileHover={{ x: -5 }}
          >
            <ArrowLeft className="h-5 w-5" />
            Back to Home
          </motion.button>
          
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Privacy Policy</h1>
          <p className="text-lg text-gray-600 mb-4">
            Your privacy is important to us. This policy explains how we collect, use, and protect your information.
          </p>
          <p className="text-sm text-gray-500">
            Last updated: January 2024
          </p>
        </motion.div>

        {/* Introduction */}
        <motion.div
          className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Our Commitment to Privacy</h2>
          <p className="text-gray-600 mb-4">
            At EliteShop, we are committed to protecting your privacy and ensuring the security of your personal information. 
            This Privacy Policy describes how we collect, use, disclose, and safeguard your information when you visit our 
            website or make a purchase from us.
          </p>
          <p className="text-gray-600">
            By using our service, you agree to the collection and use of information in accordance with this policy. 
            We will not use or share your information with anyone except as described in this Privacy Policy.
          </p>
        </motion.div>

        {/* Privacy Sections */}
        <div className="space-y-8">
          {sections.map((section, index) => {
            const Icon = section.icon
            return (
              <motion.div
                key={section.title}
                className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
              >
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
                    <Icon className="h-6 w-6 text-primary-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">{section.title}</h2>
                </div>
                
                <ul className="space-y-3">
                  {section.content.map((item, idx) => (
                    <li key={idx} className="flex items-start gap-3 text-gray-600">
                      <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            )
          })}
        </div>

        {/* Cookies Section */}
        <motion.div
          className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 mt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Cookies and Tracking</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Essential Cookies</h3>
              <p className="text-gray-600 text-sm mb-4">
                These cookies are necessary for the website to function and cannot be switched off. 
                They include shopping cart functionality, security features, and basic site operations.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Analytics Cookies</h3>
              <p className="text-gray-600 text-sm mb-4">
                These cookies help us understand how visitors interact with our website by collecting 
                and reporting information anonymously.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Marketing Cookies</h3>
              <p className="text-gray-600 text-sm mb-4">
                These cookies track your browsing habits to enable us to show advertising which is 
                more likely to be of interest to you.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Preference Cookies</h3>
              <p className="text-gray-600 text-sm mb-4">
                These cookies allow the website to remember choices you make and provide enhanced, 
                more personal features.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Contact Section */}
        <motion.div
          className="bg-primary-50 rounded-2xl p-8 mt-8 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Questions About Privacy?</h2>
          <p className="text-gray-600 mb-6">
            If you have any questions about this Privacy Policy or our data practices, 
            please don't hesitate to contact us.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.button
              className="btn-primary"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Contact Privacy Team
            </motion.button>
            <motion.button
              className="btn-secondary"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Manage Cookie Preferences
            </motion.button>
          </div>
          
          <div className="mt-6 pt-6 border-t border-primary-200">
            <p className="text-sm text-gray-500">
              Email: <EMAIL> | Phone: +****************
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default PrivacyPolicyPage
