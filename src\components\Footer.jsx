import { useState } from 'react'
import { motion } from 'framer-motion'
import { Mail, Phone, MapPin, Facebook, Twitter, Instagram, Youtube, ArrowUp, Check } from 'lucide-react'
import ContactModal from './ContactModal'
import AboutModal from './AboutModal'
import HelpCenterModal from './HelpCenterModal'

const Footer = ({ onNavigate, onPageNavigate }) => {
  const [email, setEmail] = useState('')
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [isSubscribing, setIsSubscribing] = useState(false)
  const [showContactModal, setShowContactModal] = useState(false)
  const [showAboutModal, setShowAboutModal] = useState(false)
  const [showHelpModal, setShowHelpModal] = useState(false)

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const handleNewsletterSubmit = async (e) => {
    e.preventDefault()
    if (!email.trim()) return

    setIsSubscribing(true)

    // Simulate API call
    setTimeout(() => {
      setIsSubscribed(true)
      setIsSubscribing(false)
      setEmail('')

      // Reset success message after 3 seconds
      setTimeout(() => {
        setIsSubscribed(false)
      }, 3000)
    }, 1000)
  }

  const handleLinkClick = (linkName, category) => {
    console.log(`Footer link clicked: ${category} - ${linkName}`)

    // Handle specific navigation based on link
    switch (linkName) {
      case 'All Products':
        onNavigate?.('home')
        break
      case 'New Arrivals':
        onNavigate?.('home')
        // Could add filtering logic here
        break
      case 'Best Sellers':
        onNavigate?.('home')
        // Could add filtering logic here
        break
      case 'Help Center':
        setShowHelpModal(true)
        break
      case 'Contact Us':
        setShowContactModal(true)
        break
      case 'About Us':
        setShowAboutModal(true)
        break
      case 'Sale Items':
        onNavigate?.('home')
        // Could add sale filtering logic here
        break
      case 'Gift Cards':
        alert('Gift Cards feature coming soon!')
        break
      case 'Shipping Info':
        onPageNavigate?.('shipping')
        break
      case 'Returns':
        onPageNavigate?.('returns')
        break
      case 'Size Guide':
        onPageNavigate?.('size-guide')
        break
      case 'Careers':
        onPageNavigate?.('careers')
        break
      case 'Press':
        onPageNavigate?.('press')
        break
      case 'Sustainability':
        onPageNavigate?.('sustainability')
        break
      case 'Investors':
        onPageNavigate?.('investors')
        break
      case 'Privacy Policy':
        onPageNavigate?.('privacy-policy')
        break
      case 'Terms of Service':
        onPageNavigate?.('terms-of-service')
        break
      case 'Cookie Policy':
        onPageNavigate?.('cookie-policy')
        break
      case 'Accessibility':
        onPageNavigate?.('accessibility')
        break
      default:
        alert(`${linkName} functionality coming soon!`)
    }
  }

  const handleSocialClick = (platform) => {
    console.log(`Social link clicked: ${platform}`)
    // In a real app, these would link to actual social media pages
    const socialUrls = {
      Facebook: 'https://facebook.com/eliteshop',
      Twitter: 'https://twitter.com/eliteshop',
      Instagram: 'https://instagram.com/eliteshop',
      YouTube: 'https://youtube.com/eliteshop'
    }

    // Open social media link in new tab
    window.open(socialUrls[platform] || '#', '_blank', 'noopener,noreferrer')
  }

  const footerLinks = {
    shop: [
      { name: 'All Products' },
      { name: 'New Arrivals' },
      { name: 'Best Sellers' },
      { name: 'Sale Items' },
      { name: 'Gift Cards' }
    ],
    support: [
      { name: 'Help Center' },
      { name: 'Contact Us' },
      { name: 'Shipping Info' },
      { name: 'Returns' },
      { name: 'Size Guide' }
    ],
    company: [
      { name: 'About Us' },
      { name: 'Careers' },
      { name: 'Press' },
      { name: 'Sustainability' },
      { name: 'Investors' }
    ],
    legal: [
      { name: 'Privacy Policy' },
      { name: 'Terms of Service' },
      { name: 'Cookie Policy' },
      { name: 'Accessibility' }
    ]
  }

  const socialLinks = [
    { icon: Facebook, label: 'Facebook' },
    { icon: Twitter, label: 'Twitter' },
    { icon: Instagram, label: 'Instagram' },
    { icon: Youtube, label: 'YouTube' }
  ]

  return (
    <footer className="bg-text-primary text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary-500/20 to-secondary-500/20" />
        <div className="absolute top-1/4 right-1/4 w-64 h-64 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 left-1/4 w-48 h-48 bg-gradient-to-r from-accent-coral/10 to-accent-lime/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10">
        {/* Newsletter Section */}
        <motion.div
          className="border-b border-white/10 py-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-2xl mx-auto">
              <h3 className="text-3xl font-bold mb-4">
                Stay in the <span className="text-gradient">Loop</span>
              </h3>
              <p className="text-white/80 mb-8">
                Subscribe to our newsletter for exclusive deals, new arrivals, and insider updates
              </p>
              <form onSubmit={handleNewsletterSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <div className="flex-1">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    className="w-full px-4 py-3 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    required
                    disabled={isSubscribing || isSubscribed}
                  />
                </div>
                <motion.button
                  type="submit"
                  className={`btn-primary whitespace-nowrap flex items-center gap-2 ${
                    isSubscribed ? 'bg-green-500 hover:bg-green-600' : ''
                  }`}
                  whileHover={{ scale: isSubscribed ? 1 : 1.05 }}
                  whileTap={{ scale: isSubscribed ? 1 : 0.95 }}
                  disabled={isSubscribing || isSubscribed}
                >
                  {isSubscribing ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      Subscribing...
                    </>
                  ) : isSubscribed ? (
                    <>
                      <Check className="h-4 w-4" />
                      Subscribed!
                    </>
                  ) : (
                    'Subscribe'
                  )}
                </motion.button>
              </form>
            </div>
          </div>
        </motion.div>

        {/* Main Footer Content */}
        <div className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
              {/* Brand Section */}
              <motion.div
                className="lg:col-span-2"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <h2 className="text-3xl font-bold text-gradient mb-4">
                  EliteShop
                </h2>
                <p className="text-white/80 mb-6 leading-relaxed">
                  Your premier destination for cutting-edge technology and premium lifestyle products. 
                  We curate the finest selection to elevate your everyday experience.
                </p>
                
                {/* Contact Info */}
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Mail className="h-5 w-5 text-primary-500" />
                    <span className="text-white/80"><EMAIL></span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="h-5 w-5 text-primary-500" />
                    <span className="text-white/80">+****************</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <MapPin className="h-5 w-5 text-primary-500" />
                    <span className="text-white/80">123 Innovation St, Tech City, TC 12345</span>
                  </div>
                </div>
              </motion.div>

              {/* Links Sections */}
              {Object.entries(footerLinks).map(([category, links], index) => (
                <motion.div
                  key={category}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <h4 className="text-lg font-semibold mb-4 capitalize">
                    {category === 'shop' ? 'Shop' : 
                     category === 'support' ? 'Support' :
                     category === 'company' ? 'Company' : 'Legal'}
                  </h4>
                  <ul className="space-y-3">
                    {links.map((link) => (
                      <li key={link.name}>
                        <motion.button
                          onClick={() => handleLinkClick(link.name, category)}
                          className="text-white/70 hover:text-white transition-colors duration-300 text-left"
                          whileHover={{ x: 5 }}
                        >
                          {link.name}
                        </motion.button>
                      </li>
                    ))}
                  </ul>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-white/10 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row items-center justify-between gap-6">
              {/* Copyright */}
              <motion.p
                className="text-white/60 text-center md:text-left"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                © 2024 EliteShop. All rights reserved. Crafted with ❤️ for modern shoppers.
              </motion.p>

              {/* Social Links */}
              <motion.div
                className="flex items-center gap-4"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                {socialLinks.map((social) => {
                  const IconComponent = social.icon
                  return (
                    <motion.button
                      key={social.label}
                      onClick={() => handleSocialClick(social.label)}
                      aria-label={social.label}
                      className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors duration-300"
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <IconComponent className="h-5 w-5 text-white/70 hover:text-white" />
                    </motion.button>
                  )
                })}
              </motion.div>

              {/* Back to Top */}
              <motion.button
                onClick={scrollToTop}
                className="p-3 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 hover:shadow-lg hover:shadow-primary-500/25 transition-all duration-300"
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.9 }}
                aria-label="Back to top"
              >
                <ArrowUp className="h-5 w-5 text-white" />
              </motion.button>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <ContactModal
        isOpen={showContactModal}
        onClose={() => setShowContactModal(false)}
      />
      <AboutModal
        isOpen={showAboutModal}
        onClose={() => setShowAboutModal(false)}
      />
      <HelpCenterModal
        isOpen={showHelpModal}
        onClose={() => setShowHelpModal(false)}
      />


    </footer>
  )
}

export default Footer
