import { useState } from 'react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON><PERSON><PERSON>, Ruler, User, Shirt, Footprints } from 'lucide-react'

const SizeGuidePage = ({ onBackToHome }) => {
  const [activeCategory, setActiveCategory] = useState('clothing')

  const categories = [
    { id: 'clothing', name: 'Clothing', icon: Shirt },
    { id: 'shoes', name: 'Shoes', icon: Footprints },
    { id: 'accessories', name: 'Accessories', icon: User }
  ]

  const clothingSizes = {
    men: [
      { size: 'XS', chest: '32-34', waist: '28-30', length: '27' },
      { size: 'S', chest: '34-36', waist: '30-32', length: '28' },
      { size: 'M', chest: '36-38', waist: '32-34', length: '29' },
      { size: 'L', chest: '38-40', waist: '34-36', length: '30' },
      { size: 'XL', chest: '40-42', waist: '36-38', length: '31' },
      { size: 'XXL', chest: '42-44', waist: '38-40', length: '32' }
    ],
    women: [
      { size: 'XS', chest: '30-32', waist: '24-26', hips: '34-36' },
      { size: 'S', chest: '32-34', waist: '26-28', hips: '36-38' },
      { size: 'M', chest: '34-36', waist: '28-30', hips: '38-40' },
      { size: 'L', chest: '36-38', waist: '30-32', hips: '40-42' },
      { size: 'XL', chest: '38-40', waist: '32-34', hips: '42-44' },
      { size: 'XXL', chest: '40-42', waist: '34-36', hips: '44-46' }
    ]
  }

  const shoeSizes = {
    men: [
      { us: '7', uk: '6', eu: '40', cm: '25' },
      { us: '7.5', uk: '6.5', eu: '40.5', cm: '25.5' },
      { us: '8', uk: '7', eu: '41', cm: '26' },
      { us: '8.5', uk: '7.5', eu: '42', cm: '26.5' },
      { us: '9', uk: '8', eu: '42.5', cm: '27' },
      { us: '9.5', uk: '8.5', eu: '43', cm: '27.5' },
      { us: '10', uk: '9', eu: '44', cm: '28' },
      { us: '10.5', uk: '9.5', eu: '44.5', cm: '28.5' },
      { us: '11', uk: '10', eu: '45', cm: '29' },
      { us: '12', uk: '11', eu: '46', cm: '30' }
    ],
    women: [
      { us: '5', uk: '2.5', eu: '35', cm: '22' },
      { us: '5.5', uk: '3', eu: '35.5', cm: '22.5' },
      { us: '6', uk: '3.5', eu: '36', cm: '23' },
      { us: '6.5', uk: '4', eu: '37', cm: '23.5' },
      { us: '7', uk: '4.5', eu: '37.5', cm: '24' },
      { us: '7.5', uk: '5', eu: '38', cm: '24.5' },
      { us: '8', uk: '5.5', eu: '39', cm: '25' },
      { us: '8.5', uk: '6', eu: '39.5', cm: '25.5' },
      { us: '9', uk: '6.5', eu: '40', cm: '26' },
      { us: '10', uk: '7.5', eu: '41', cm: '27' }
    ]
  }

  const measurementTips = [
    {
      title: 'Chest/Bust',
      description: 'Measure around the fullest part of your chest, keeping the tape horizontal',
      icon: User
    },
    {
      title: 'Waist',
      description: 'Measure around your natural waistline, keeping the tape comfortably loose',
      icon: User
    },
    {
      title: 'Hips',
      description: 'Measure around the fullest part of your hips, about 8 inches below your waist',
      icon: User
    },
    {
      title: 'Foot Length',
      description: 'Stand on paper, mark heel and longest toe, then measure the distance',
      icon: Footprints
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.button
            onClick={onBackToHome}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-6"
            whileHover={{ x: -5 }}
          >
            <ArrowLeft className="h-5 w-5" />
            Back to Home
          </motion.button>
          
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Size Guide</h1>
          <p className="text-lg text-gray-600">
            Find your perfect fit with our comprehensive sizing charts
          </p>
        </motion.div>

        {/* Category Tabs */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="flex flex-wrap gap-4">
            {categories.map((category) => {
              const Icon = category.icon
              return (
                <motion.button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`flex items-center gap-2 px-6 py-3 rounded-xl font-medium transition-all ${
                    activeCategory === category.id
                      ? 'bg-primary-500 text-white shadow-lg'
                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Icon className="h-5 w-5" />
                  {category.name}
                </motion.button>
              )
            })}
          </div>
        </motion.div>

        {/* Measurement Tips */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6">How to Measure</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {measurementTips.map((tip, index) => {
              const Icon = tip.icon
              return (
                <motion.div
                  key={tip.title}
                  className="bg-white rounded-xl p-6 shadow-sm border border-gray-200"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                  whileHover={{ y: -5 }}
                >
                  <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mb-4">
                    <Icon className="h-6 w-6 text-primary-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">{tip.title}</h3>
                  <p className="text-sm text-gray-600">{tip.description}</p>
                </motion.div>
              )
            })}
          </div>
        </motion.div>

        {/* Size Charts */}
        {activeCategory === 'clothing' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Clothing Size Charts</h2>
            
            {/* Men's Clothing */}
            <div className="mb-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Men's Clothing (inches)</h3>
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-sm font-medium text-gray-900">Size</th>
                        <th className="px-6 py-3 text-left text-sm font-medium text-gray-900">Chest</th>
                        <th className="px-6 py-3 text-left text-sm font-medium text-gray-900">Waist</th>
                        <th className="px-6 py-3 text-left text-sm font-medium text-gray-900">Length</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {clothingSizes.men.map((size, index) => (
                        <tr key={size.size} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          <td className="px-6 py-4 text-sm font-medium text-gray-900">{size.size}</td>
                          <td className="px-6 py-4 text-sm text-gray-600">{size.chest}</td>
                          <td className="px-6 py-4 text-sm text-gray-600">{size.waist}</td>
                          <td className="px-6 py-4 text-sm text-gray-600">{size.length}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Women's Clothing */}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Women's Clothing (inches)</h3>
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-sm font-medium text-gray-900">Size</th>
                        <th className="px-6 py-3 text-left text-sm font-medium text-gray-900">Chest/Bust</th>
                        <th className="px-6 py-3 text-left text-sm font-medium text-gray-900">Waist</th>
                        <th className="px-6 py-3 text-left text-sm font-medium text-gray-900">Hips</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {clothingSizes.women.map((size, index) => (
                        <tr key={size.size} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          <td className="px-6 py-4 text-sm font-medium text-gray-900">{size.size}</td>
                          <td className="px-6 py-4 text-sm text-gray-600">{size.chest}</td>
                          <td className="px-6 py-4 text-sm text-gray-600">{size.waist}</td>
                          <td className="px-6 py-4 text-sm text-gray-600">{size.hips}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {activeCategory === 'shoes' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Shoe Size Charts</h2>
            
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Men's Shoes */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Men's Shoes</h3>
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">US</th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">UK</th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">EU</th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">CM</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {shoeSizes.men.map((size, index) => (
                          <tr key={size.us} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                            <td className="px-4 py-3 text-sm font-medium text-gray-900">{size.us}</td>
                            <td className="px-4 py-3 text-sm text-gray-600">{size.uk}</td>
                            <td className="px-4 py-3 text-sm text-gray-600">{size.eu}</td>
                            <td className="px-4 py-3 text-sm text-gray-600">{size.cm}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              {/* Women's Shoes */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Women's Shoes</h3>
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">US</th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">UK</th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">EU</th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">CM</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {shoeSizes.women.map((size, index) => (
                          <tr key={size.us} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                            <td className="px-4 py-3 text-sm font-medium text-gray-900">{size.us}</td>
                            <td className="px-4 py-3 text-sm text-gray-600">{size.uk}</td>
                            <td className="px-4 py-3 text-sm text-gray-600">{size.eu}</td>
                            <td className="px-4 py-3 text-sm text-gray-600">{size.cm}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {activeCategory === 'accessories' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center py-12"
          >
            <Ruler className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Accessories Sizing</h3>
            <p className="text-gray-600 max-w-md mx-auto">
              Most accessories are one-size-fits-all or have adjustable features. 
              Specific sizing information is provided on individual product pages.
            </p>
          </motion.div>
        )}

        {/* Contact Section */}
        <motion.div
          className="mt-12 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Still Need Help?</h3>
          <p className="text-gray-600 mb-6">
            Our sizing experts are here to help you find the perfect fit.
          </p>
          <motion.button
            className="btn-primary"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Contact Sizing Support
          </motion.button>
        </motion.div>
      </div>
    </div>
  )
}

export default SizeGuidePage
