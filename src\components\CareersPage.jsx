import { motion } from 'framer-motion'
import { ArrowLeft, Users, Briefcase, Heart, Zap, Globe, Coffee, Laptop, MapPin } from 'lucide-react'

const CareersPage = ({ onBackToHome }) => {
  const benefits = [
    {
      icon: Heart,
      title: 'Health & Wellness',
      description: 'Comprehensive health insurance, dental, vision, and wellness programs'
    },
    {
      icon: Laptop,
      title: 'Remote Flexibility',
      description: 'Work from anywhere with flexible hours and remote-first culture'
    },
    {
      icon: Zap,
      title: 'Growth Opportunities',
      description: 'Professional development budget and career advancement paths'
    },
    {
      icon: Coffee,
      title: 'Work-Life Balance',
      description: 'Unlimited PTO, mental health days, and family-friendly policies'
    },
    {
      icon: Users,
      title: 'Amazing Team',
      description: 'Collaborative environment with passionate and talented colleagues'
    },
    {
      icon: Globe,
      title: 'Global Impact',
      description: 'Work on products that reach millions of customers worldwide'
    }
  ]

  const openPositions = [
    {
      title: 'Senior Frontend Developer',
      department: 'Engineering',
      location: 'Remote / San Francisco',
      type: 'Full-time',
      description: 'Build beautiful, responsive user interfaces using React and modern web technologies.'
    },
    {
      title: 'Product Manager',
      department: 'Product',
      location: 'Remote / New York',
      type: 'Full-time',
      description: 'Drive product strategy and work with cross-functional teams to deliver amazing experiences.'
    },
    {
      title: 'UX/UI Designer',
      department: 'Design',
      location: 'Remote / Los Angeles',
      type: 'Full-time',
      description: 'Create intuitive and delightful user experiences for our e-commerce platform.'
    },
    {
      title: 'Data Scientist',
      department: 'Analytics',
      location: 'Remote / Austin',
      type: 'Full-time',
      description: 'Analyze customer behavior and build ML models to improve personalization.'
    },
    {
      title: 'Customer Success Manager',
      department: 'Customer Success',
      location: 'Remote / Chicago',
      type: 'Full-time',
      description: 'Help our customers succeed and grow their business with our platform.'
    },
    {
      title: 'Marketing Specialist',
      department: 'Marketing',
      location: 'Remote / Miami',
      type: 'Full-time',
      description: 'Drive growth through creative marketing campaigns and customer acquisition.'
    }
  ]

  const values = [
    {
      title: 'Customer Obsession',
      description: 'We put customers at the center of everything we do'
    },
    {
      title: 'Innovation',
      description: 'We constantly push boundaries and embrace new technologies'
    },
    {
      title: 'Transparency',
      description: 'We communicate openly and honestly with each other'
    },
    {
      title: 'Excellence',
      description: 'We strive for the highest quality in everything we deliver'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          className="mb-12 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.button
            onClick={onBackToHome}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-6 mx-auto"
            whileHover={{ x: -5 }}
          >
            <ArrowLeft className="h-5 w-5" />
            Back to Home
          </motion.button>
          
          <h1 className="text-5xl font-bold text-gray-900 mb-6">Join Our Team</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Help us build the future of e-commerce. We're looking for passionate, talented people 
            who want to make a difference in how people shop online.
          </p>
        </motion.div>

        {/* Company Values */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Our Values</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                whileHover={{ y: -5 }}
              >
                <h3 className="font-semibold text-gray-900 mb-2">{value.title}</h3>
                <p className="text-sm text-gray-600">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Benefits */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Why Work With Us</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon
              return (
                <motion.div
                  key={benefit.title}
                  className="bg-white rounded-xl p-6 shadow-sm border border-gray-200"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                  whileHover={{ y: -5 }}
                >
                  <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mb-4">
                    <Icon className="h-6 w-6 text-primary-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">{benefit.title}</h3>
                  <p className="text-gray-600 text-sm">{benefit.description}</p>
                </motion.div>
              )
            })}
          </div>
        </motion.div>

        {/* Open Positions */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Open Positions</h2>
          <div className="space-y-6">
            {openPositions.map((position, index) => (
              <motion.div
                key={position.title}
                className="bg-white rounded-xl p-6 shadow-sm border border-gray-200"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.7 + index * 0.1 }}
                whileHover={{ x: 5 }}
              >
                <div className="flex flex-col md:flex-row md:items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-4 mb-2">
                      <h3 className="text-xl font-semibold text-gray-900">{position.title}</h3>
                      <span className="px-3 py-1 bg-primary-100 text-primary-700 text-sm font-medium rounded-full">
                        {position.department}
                      </span>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4" />
                        {position.location}
                      </div>
                      <div className="flex items-center gap-1">
                        <Briefcase className="h-4 w-4" />
                        {position.type}
                      </div>
                    </div>
                    <p className="text-gray-600">{position.description}</p>
                  </div>
                  <div className="mt-4 md:mt-0 md:ml-6">
                    <motion.button
                      className="btn-primary"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Apply Now
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Application Process */}
        <motion.div
          className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Our Hiring Process</h2>
          <div className="grid md:grid-cols-4 gap-6">
            {[
              { step: 1, title: 'Apply', description: 'Submit your application and resume' },
              { step: 2, title: 'Screen', description: 'Initial phone or video screening' },
              { step: 3, title: 'Interview', description: 'Technical and cultural fit interviews' },
              { step: 4, title: 'Offer', description: 'Reference check and job offer' }
            ].map((step, index) => (
              <motion.div
                key={step.step}
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.9 + index * 0.1 }}
              >
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-primary-600">{step.step}</span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">{step.title}</h3>
                <p className="text-sm text-gray-600">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Contact Section */}
        <motion.div
          className="bg-primary-50 rounded-2xl p-8 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Don't See a Perfect Fit?</h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            We're always looking for talented people to join our team. Send us your resume 
            and tell us how you'd like to contribute to EliteShop's mission.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.button
              className="btn-primary"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Send General Application
            </motion.button>
            <motion.button
              className="btn-secondary"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Join Our Talent Pool
            </motion.button>
          </div>
          
          <div className="mt-6 pt-6 border-t border-primary-200">
            <p className="text-sm text-gray-500">
              Email: <EMAIL> | Follow us on LinkedIn for updates
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default CareersPage
