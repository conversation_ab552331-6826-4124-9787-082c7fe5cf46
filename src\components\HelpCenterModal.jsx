import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Search, ChevronDown, ChevronRight, MessageCircle, Phone, Mail, Book } from 'lucide-react'

const HelpCenterModal = ({ isOpen, onClose }) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [expandedFaq, setExpandedFaq] = useState(null)
  const [activeCategory, setActiveCategory] = useState('general')

  const categories = [
    { id: 'general', name: 'General', icon: Book },
    { id: 'orders', name: 'Orders & Shipping', icon: MessageCircle },
    { id: 'returns', name: 'Returns & Refunds', icon: Phone },
    { id: 'account', name: 'Account & Billing', icon: Mail }
  ]

  const faqs = {
    general: [
      {
        question: 'What is EliteShop?',
        answer: 'EliteShop is a premium e-commerce platform offering curated technology and lifestyle products. We focus on quality, innovation, and exceptional customer service.'
      },
      {
        question: 'How do I create an account?',
        answer: 'Click the profile icon in the top right corner and select "Create Account". Fill in your details and verify your email address to get started.'
      },
      {
        question: 'Is my personal information secure?',
        answer: 'Yes, we use industry-standard encryption and security measures to protect your personal information. We never share your data with third parties without your consent.'
      },
      {
        question: 'Do you offer international shipping?',
        answer: 'Yes, we ship to over 25 countries worldwide. Shipping costs and delivery times vary by location. Check our shipping page for detailed information.'
      }
    ],
    orders: [
      {
        question: 'How can I track my order?',
        answer: 'Once your order ships, you\'ll receive a tracking number via email. You can also check your order status in your account dashboard.'
      },
      {
        question: 'What are your shipping options?',
        answer: 'We offer Standard Shipping (5-7 business days) and Express Shipping (2-3 business days). Shipping costs are calculated at checkout based on your location.'
      },
      {
        question: 'Can I change or cancel my order?',
        answer: 'You can modify or cancel your order within 1 hour of placing it. After that, please contact our customer service team for assistance.'
      },
      {
        question: 'What if my package is damaged or lost?',
        answer: 'If your package arrives damaged or goes missing, contact us immediately. We\'ll work with the shipping carrier to resolve the issue and ensure you receive your order.'
      }
    ],
    returns: [
      {
        question: 'What is your return policy?',
        answer: 'We offer a 30-day return policy for most items. Products must be in original condition with all packaging and accessories included.'
      },
      {
        question: 'How do I initiate a return?',
        answer: 'Log into your account, go to Order History, and select "Return Item" next to the product you want to return. Follow the instructions to print a return label.'
      },
      {
        question: 'When will I receive my refund?',
        answer: 'Refunds are processed within 3-5 business days after we receive your returned item. The refund will appear on your original payment method within 5-10 business days.'
      },
      {
        question: 'Can I exchange an item instead of returning it?',
        answer: 'Yes, you can exchange items for a different size, color, or model (if available). The exchange process is similar to returns but you select "Exchange" instead.'
      }
    ],
    account: [
      {
        question: 'How do I reset my password?',
        answer: 'Click "Forgot Password" on the login page, enter your email address, and follow the instructions in the reset email we send you.'
      },
      {
        question: 'How do I update my billing information?',
        answer: 'Go to your Account Settings, select "Payment Methods", and add, edit, or remove your payment information as needed.'
      },
      {
        question: 'Can I save multiple addresses?',
        answer: 'Yes, you can save multiple shipping and billing addresses in your account. This makes checkout faster for future orders.'
      },
      {
        question: 'How do I delete my account?',
        answer: 'Contact our customer service team to request account deletion. Please note that this action is permanent and cannot be undone.'
      }
    ]
  }

  const filteredFaqs = faqs[activeCategory].filter(faq =>
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
  )

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="bg-white rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
          initial={{ scale: 0.8, opacity: 0, y: 50 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.8, opacity: 0, y: 50 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900">Help Center</h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="flex h-[calc(90vh-120px)]">
            {/* Sidebar */}
            <div className="w-1/3 border-r border-gray-200 p-6">
              <div className="mb-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search help articles..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-3">
                  Categories
                </h3>
                {categories.map((category) => {
                  const IconComponent = category.icon
                  return (
                    <motion.button
                      key={category.id}
                      onClick={() => setActiveCategory(category.id)}
                      className={`w-full flex items-center gap-3 p-3 rounded-xl text-left transition-colors ${
                        activeCategory === category.id
                          ? 'bg-primary-50 text-primary-700 border border-primary-200'
                          : 'hover:bg-gray-50 text-gray-700'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <IconComponent className="h-5 w-5" />
                      <span className="font-medium">{category.name}</span>
                    </motion.button>
                  )
                })}
              </div>

              <div className="mt-8 p-4 bg-gray-50 rounded-xl">
                <h4 className="font-semibold text-gray-900 mb-2">Still need help?</h4>
                <p className="text-sm text-gray-600 mb-3">
                  Can't find what you're looking for? Our support team is here to help.
                </p>
                <motion.button
                  className="w-full btn-primary text-sm py-2"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => {
                    onClose()
                    alert('Contact support form would open here')
                  }}
                >
                  Contact Support
                </motion.button>
              </div>
            </div>

            {/* Main Content */}
            <div className="flex-1 p-6 overflow-y-auto">
              <div className="mb-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {categories.find(cat => cat.id === activeCategory)?.name}
                </h3>
                <p className="text-gray-600">
                  {searchQuery ? `Search results for "${searchQuery}"` : 'Frequently asked questions'}
                </p>
              </div>

              <div className="space-y-4">
                {filteredFaqs.length > 0 ? (
                  filteredFaqs.map((faq, index) => (
                    <motion.div
                      key={index}
                      className="border border-gray-200 rounded-xl overflow-hidden"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <button
                        onClick={() => setExpandedFaq(expandedFaq === index ? null : index)}
                        className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
                      >
                        <span className="font-medium text-gray-900">{faq.question}</span>
                        {expandedFaq === index ? (
                          <ChevronDown className="h-5 w-5 text-gray-500" />
                        ) : (
                          <ChevronRight className="h-5 w-5 text-gray-500" />
                        )}
                      </button>
                      
                      <AnimatePresence>
                        {expandedFaq === index && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3 }}
                          >
                            <div className="px-4 pb-4 text-gray-600 border-t border-gray-100">
                              <div className="pt-4">
                                {faq.answer}
                              </div>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  ))
                ) : (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Search className="h-8 w-8 text-gray-400" />
                    </div>
                    <h4 className="text-lg font-medium text-gray-900 mb-2">No results found</h4>
                    <p className="text-gray-600">
                      Try adjusting your search terms or browse our categories.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default HelpCenterModal
