import { motion } from 'framer-motion'
import { ArrowLeft, Heart, ShoppingCart, Trash2 } from 'lucide-react'
import { getProductById } from '../data/products'
import ProductCard from './ProductCard'

const WishlistPage = ({
  wishlist,
  onAddToCart,
  onToggleWishlist,
  onProductClick,
  onQuickView,
  onBackToHome
}) => {
  const wishlistProducts = Array.from(wishlist).map(id => getProductById(id)).filter(Boolean)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  }

  const handleClearWishlist = () => {
    wishlistProducts.forEach(product => {
      onToggleWishlist(product.id)
    })
  }

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          className="flex items-center justify-between mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center space-x-4">
            <motion.button
              onClick={onBackToHome}
              className="p-2 rounded-xl glass-card hover:bg-white/90 transition-all duration-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </motion.button>
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-800">
                My <span className="text-gradient">Wishlist</span>
              </h1>
              <p className="text-gray-600 mt-1">
                {wishlistProducts.length} item{wishlistProducts.length !== 1 ? 's' : ''} saved
              </p>
            </div>
          </div>

          {wishlistProducts.length > 0 && (
            <motion.button
              onClick={handleClearWishlist}
              className="flex items-center space-x-2 px-4 py-2 text-red-600 hover:bg-red-50 rounded-xl transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Trash2 className="h-4 w-4" />
              <span>Clear All</span>
            </motion.button>
          )}
        </motion.div>

        {/* Wishlist Content */}
        {wishlistProducts.length === 0 ? (
          <motion.div
            className="text-center py-16"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <div className="w-24 h-24 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <Heart className="h-12 w-12 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Your wishlist is empty</h2>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              Start adding products to your wishlist by clicking the heart icon on any product.
            </p>
            <motion.button
              onClick={onBackToHome}
              className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-8 py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Continue Shopping
            </motion.button>
          </motion.div>
        ) : (
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {wishlistProducts.map((product) => (
              <motion.div key={product.id} variants={itemVariants}>
                <ProductCard
                  product={product}
                  onAddToCart={onAddToCart}
                  onToggleWishlist={onToggleWishlist}
                  onProductClick={onProductClick}
                  onQuickView={onQuickView}
                  isWishlisted={true}
                  showQuickAdd={true}
                />
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Wishlist Actions */}
        {wishlistProducts.length > 0 && (
          <motion.div
            className="mt-12 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="glass-card rounded-2xl p-6 max-w-md mx-auto">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">
                Ready to purchase?
              </h3>
              <p className="text-gray-600 mb-6">
                Add all items to your cart and proceed to checkout.
              </p>
              <motion.button
                onClick={() => {
                  wishlistProducts.forEach(product => {
                    onAddToCart({ ...product, quantity: 1 })
                  })
                }}
                className="w-full bg-gradient-to-r from-primary-500 to-secondary-500 text-white py-3 px-6 rounded-xl font-medium hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-2"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <ShoppingCart className="h-5 w-5" />
                <span>Add All to Cart</span>
              </motion.button>
            </div>
          </motion.div>
        )}

        {/* Recommendations */}
        {wishlistProducts.length > 0 && (
          <motion.div
            className="mt-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <h2 className="text-2xl font-bold text-gray-800 mb-6">
              You might also like
            </h2>
            <div className="glass-card rounded-2xl p-6">
              <p className="text-gray-600 text-center">
                Personalized recommendations based on your wishlist will appear here.
              </p>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}

export default WishlistPage
