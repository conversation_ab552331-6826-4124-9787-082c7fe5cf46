/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#e6fffe',
          100: '#ccfffc',
          500: '#0ff0fc',
          600: '#0dd9e6',
          700: '#0bc2d0',
        },
        secondary: {
          50: '#f3e8ff',
          100: '#e7d1ff',
          500: '#a259ff',
          600: '#9147e6',
          700: '#8035cc',
        },
        accent: {
          coral: '#ff6f61',
          lime: '#cfff04',
        },
        'bg-primary': '#fafbfc',
        'bg-secondary': '#f5f5f7',
        'text-primary': '#222222',
        'text-secondary': '#43464e',
      },
      fontFamily: {
        'geometric': ['Inter', 'Montserrat', 'system-ui', 'sans-serif'],
      },
      backdropBlur: {
        xs: '2px',
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'slide-up': 'slideUp 0.3s ease-out',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        glow: {
          '0%': { boxShadow: '0 0 5px rgba(15, 240, 252, 0.5)' },
          '100%': { boxShadow: '0 0 20px rgba(15, 240, 252, 0.8)' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        }
      }
    },
  },
  plugins: [],
}
