import { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { ChevronLeft, ChevronRight, Sparkles } from 'lucide-react'
import { products } from '../data/products'
import ProductCard from './ProductCard'

const PersonalizedRecommendations = ({ onAddToCart, onToggleWishlist, onProductClick, onQuickView, onRegister, wishlist = new Set() }) => {
  const scrollRef = useRef(null)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(true)
  const [isLoading, setIsLoading] = useState(true)

  // Enhanced AI recommendation algorithm
  const getAIRecommendations = () => {
    // Simulate user behavior data (in real app, this would come from user analytics)
    const userPreferences = {
      categories: ['electronics', 'fashion', 'sports-fitness'],
      priceRange: { min: 50, max: 500 },
      brands: ['Apple', 'Nike', 'Samsung'],
      recentViews: ['smartphone', 'headphones', 'watch'],
      purchaseHistory: ['electronics', 'fashion']
    }

    // AI scoring algorithm
    const scoreProduct = (product) => {
      let score = 0

      // Category preference (40% weight)
      if (userPreferences.categories.includes(product.category)) {
        score += 40
      }

      // Price preference (20% weight)
      if (product.price >= userPreferences.priceRange.min &&
          product.price <= userPreferences.priceRange.max) {
        score += 20
      }

      // Rating boost (15% weight)
      score += (product.rating / 5) * 15

      // Popularity boost (10% weight)
      score += Math.min((product.reviews / 1000) * 10, 10)

      // Stock availability (10% weight)
      if (product.inStock && product.stock > 5) {
        score += 10
      }

      // Trending boost (5% weight)
      if (product.badge === 'Trending' || product.badge === 'Hot') {
        score += 5
      }

      return score
    }

    // Get top scored products
    const scoredProducts = products
      .map(product => ({
        ...product,
        aiScore: scoreProduct(product),
        reason: getPersonalizedReason(product, userPreferences)
      }))
      .sort((a, b) => b.aiScore - a.aiScore)
      .slice(0, 8)

    return scoredProducts
  }

  function getPersonalizedReason(product, preferences) {
    const reasons = {
      electronics: [
        "Perfect match for tech enthusiasts",
        "Trending in your favorite category",
        "High-rated by users like you",
        "Based on your recent tech searches"
      ],
      fashion: [
        "Matches your style profile",
        "Popular among fashion-forward users",
        "Perfect for your wardrobe",
        "Trending in fashion this season"
      ],
      "home-garden": [
        "Great for home improvement",
        "Popular home essential",
        "Perfect for your living space",
        "Highly rated home product"
      ],
      "sports-fitness": [
        "Ideal for your active lifestyle",
        "Top choice for fitness enthusiasts",
        "Perfect for your workout routine",
        "Trending in fitness community"
      ],
      "beauty-health": [
        "Great for your wellness routine",
        "Popular beauty choice",
        "Perfect for self-care",
        "Highly recommended for health"
      ],
      "books-media": [
        "Based on your reading interests",
        "Popular among book lovers",
        "Perfect for entertainment",
        "Trending in media category"
      ]
    }

    const categoryReasons = reasons[product.category] || ["Recommended for you"]
    const randomReason = categoryReasons[Math.floor(Math.random() * categoryReasons.length)]

    // Add personalization based on product attributes
    if (product.rating >= 4.5) {
      return `${randomReason} • ⭐ Highly rated`
    } else if (product.badge === 'Hot' || product.badge === 'Trending') {
      return `${randomReason} • 🔥 Trending now`
    } else if (product.originalPrice && product.originalPrice > product.price) {
      return `${randomReason} • 💰 Great deal`
    }

    return randomReason
  }

  const recommendations = getAIRecommendations()

  // Simulate AI processing time
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1500) // 1.5 second loading simulation

    return () => clearTimeout(timer)
  }, [])

  // Enhanced scroll functionality with mouse wheel support
  useEffect(() => {
    const container = scrollRef.current
    if (!container) return

    const handleWheel = (e) => {
      // Only handle horizontal scroll when shift is pressed or when scrolling horizontally
      if (e.shiftKey || Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
        e.preventDefault()
        const scrollAmount = e.deltaY || e.deltaX
        container.scrollLeft += scrollAmount
        updateScrollButtons()
      }
    }

    const handleScroll = () => {
      updateScrollButtons()
    }

    const updateScrollButtons = () => {
      setCanScrollLeft(container.scrollLeft > 0)
      setCanScrollRight(
        container.scrollLeft < container.scrollWidth - container.clientWidth - 1
      )
    }

    container.addEventListener('wheel', handleWheel, { passive: false })
    container.addEventListener('scroll', handleScroll)

    // Initial button state
    updateScrollButtons()

    return () => {
      container.removeEventListener('wheel', handleWheel)
      container.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const scroll = (direction) => {
    const container = scrollRef.current
    if (!container) return

    const scrollAmount = 320
    const newScrollLeft = direction === 'left' 
      ? container.scrollLeft - scrollAmount
      : container.scrollLeft + scrollAmount

    container.scrollTo({
      left: newScrollLeft,
      behavior: 'smooth'
    })

    // Update scroll button states
    setTimeout(() => {
      setCanScrollLeft(container.scrollLeft > 0)
      setCanScrollRight(
        container.scrollLeft < container.scrollWidth - container.clientWidth
      )
    }, 300)
  }

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="flex items-center justify-between mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div>
            <div className="flex items-center gap-3 mb-2">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <Sparkles className="h-6 w-6 text-primary-500" />
              </motion.div>
              <h2 className="text-4xl md:text-5xl font-bold" style={{ color: '#222222' }}>
                Just for <span className="text-gradient">You</span>
              </h2>
              <div className="px-3 py-1 bg-gradient-to-r from-primary-500 to-secondary-500 text-white text-xs font-medium rounded-full">
                AI Powered
              </div>
            </div>
            <p className="text-lg" style={{ color: '#43464e' }}>
              Smart recommendations powered by AI • Scroll horizontally or use Shift+Wheel
            </p>
          </div>

          {/* Navigation Buttons */}
          <div className="hidden md:flex gap-2">
            <motion.button
              onClick={() => scroll('left')}
              disabled={!canScrollLeft}
              className={`p-3 rounded-xl transition-all duration-300 ${
                canScrollLeft
                  ? 'glass-card hover:bg-white/90'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }`}
              style={canScrollLeft ? { color: '#222222' } : {}}
              whileHover={canScrollLeft ? { scale: 1.05 } : {}}
              whileTap={canScrollLeft ? { scale: 0.95 } : {}}
            >
              <ChevronLeft className="h-5 w-5" />
            </motion.button>
            <motion.button
              onClick={() => scroll('right')}
              disabled={!canScrollRight}
              className={`p-3 rounded-xl transition-all duration-300 ${
                canScrollRight
                  ? 'glass-card hover:bg-white/90'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }`}
              style={canScrollRight ? { color: '#222222' } : {}}
              whileHover={canScrollRight ? { scale: 1.05 } : {}}
              whileTap={canScrollRight ? { scale: 0.95 } : {}}
            >
              <ChevronRight className="h-5 w-5" />
            </motion.button>
          </div>
        </motion.div>

        {/* Products Scroll Container */}
        <motion.div
          className="relative"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {/* Scroll Indicators */}
          {canScrollLeft && (
            <div className="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-white to-transparent z-30 pointer-events-none" />
          )}
          {canScrollRight && (
            <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-white to-transparent z-30 pointer-events-none" />
          )}

          <div
            ref={scrollRef}
            className="flex gap-6 overflow-x-auto scrollbar-hide pb-4 scroll-smooth"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {isLoading ? (
              // AI Loading State
              Array.from({ length: 4 }).map((_, index) => (
                <motion.div
                  key={`loading-${index}`}
                  className="flex-shrink-0 w-80"
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <div className="glass-card rounded-2xl overflow-hidden h-96 flex flex-col">
                    {/* Loading Image */}
                    <div className="relative h-64 bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse">
                      <div className="absolute top-4 left-4 px-3 py-1 bg-gradient-to-r from-primary-500 to-secondary-500 text-white text-xs font-medium rounded-full flex items-center gap-1">
                        <Sparkles className="h-3 w-3 animate-spin" />
                        AI Analyzing
                      </div>
                    </div>

                    {/* Loading Content */}
                    <div className="p-6 flex-1 flex flex-col">
                      <div className="h-6 bg-gray-200 rounded animate-pulse mb-2" />
                      <div className="h-4 bg-gray-200 rounded animate-pulse mb-4 w-3/4" />
                      <div className="h-4 bg-gray-200 rounded animate-pulse mb-4 w-1/2" />
                      <div className="mt-auto">
                        <div className="h-12 bg-gradient-to-r from-gray-200 to-gray-300 rounded-xl animate-pulse" />
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))
            ) : (
              recommendations.map((product, index) => (
              <motion.div
                key={product.id}
                className="flex-shrink-0 w-80 group"
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div className="relative h-full">
                  {/* AI Badge Overlay with Tooltip */}
                  <div className="absolute top-4 left-4 z-20 group/ai">
                    <motion.div
                      className="px-3 py-1 bg-gradient-to-r from-primary-500 to-secondary-500 text-white text-xs font-medium rounded-full flex items-center gap-1 shadow-lg cursor-help"
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ delay: 0.5 + index * 0.1, duration: 0.3 }}
                      whileHover={{ scale: 1.05 }}
                    >
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                      >
                        <Sparkles className="h-3 w-3" />
                      </motion.div>
                      AI Pick
                    </motion.div>

                    {/* Tooltip with recommendation reason */}
                    <div className="absolute top-full left-0 mt-2 opacity-0 group-hover/ai:opacity-100 transition-opacity duration-300 pointer-events-none">
                      <div className="bg-black/90 text-white text-xs rounded-lg p-2 whitespace-nowrap shadow-xl">
                        {product.reason}
                        <div className="absolute -top-1 left-4 w-2 h-2 bg-black/90 rotate-45"></div>
                      </div>
                    </div>
                  </div>

                  <ProductCard
                    product={product}
                    onAddToCart={onAddToCart}
                    onToggleWishlist={onToggleWishlist}
                    onProductClick={onProductClick}
                    onQuickView={onQuickView}
                    isWishlisted={wishlist.has(product.id)}
                    className="h-full"
                  />
                </div>
              </motion.div>
              ))
            )}
          </div>

          {/* Mobile Scroll Hint */}
          <div className="flex justify-center mt-6 md:hidden">
            <div className="flex flex-col items-center gap-2">
              <div className="flex gap-2">
                {recommendations.map((_, index) => (
                  <div
                    key={index}
                    className="w-2 h-2 rounded-full bg-text-secondary/30"
                  />
                ))}
              </div>
              <p className="text-xs text-gray-500 flex items-center gap-1">
                <span>👈</span> Swipe to explore more <span>👉</span>
              </p>
            </div>
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          className="text-center mt-16 p-8 glass-card rounded-2xl"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Sparkles className="h-12 w-12 text-primary-500 mx-auto mb-4" />
          <h3 className="text-2xl font-bold mb-2" style={{ color: '#222222' }}>
            Want More Personalized Recommendations?
          </h3>
          <p className="mb-6" style={{ color: '#43464e' }}>
            Sign up for an account to get AI-powered suggestions tailored just for you
          </p>
          <motion.button
            className="btn-primary"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => {
              // Scroll to top and trigger profile dropdown
              window.scrollTo({ top: 0, behavior: 'smooth' })
              // Small delay to ensure scroll completes, then trigger register
              setTimeout(() => {
                const profileButton = document.querySelector('[data-profile-button]')
                if (profileButton) {
                  profileButton.click()
                  // Another small delay to open dropdown, then trigger register
                  setTimeout(() => {
                    const registerButton = document.querySelector('[data-register-button]')
                    if (registerButton) {
                      registerButton.click()
                    }
                  }, 100)
                }
              }, 500)
            }}
          >
            Create Account
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default PersonalizedRecommendations
