import { motion } from 'framer-motion'
import { ArrowLeft, RotateCcw, Calendar, Package, CreditCard, CheckCircle, AlertCircle } from 'lucide-react'

const ReturnsPage = ({ onBackToHome }) => {
  const returnSteps = [
    {
      step: 1,
      title: 'Initiate Return',
      description: 'Log into your account and select the item you want to return',
      icon: RotateCcw
    },
    {
      step: 2,
      title: 'Print Label',
      description: 'Download and print the prepaid return shipping label',
      icon: Package
    },
    {
      step: 3,
      title: 'Ship Item',
      description: 'Package the item securely and ship using the provided label',
      icon: Package
    },
    {
      step: 4,
      title: 'Get Refund',
      description: 'Receive your refund within 5-10 business days after we receive the item',
      icon: CreditCard
    }
  ]

  const returnPolicies = [
    {
      title: '30-Day Return Window',
      description: 'Items can be returned within 30 days of delivery for a full refund',
      icon: Calendar,
      color: 'bg-green-100 text-green-600'
    },
    {
      title: 'Original Condition Required',
      description: 'Items must be unused, unworn, and in original packaging with all tags attached',
      icon: CheckCircle,
      color: 'bg-blue-100 text-blue-600'
    },
    {
      title: 'Free Return Shipping',
      description: 'We provide prepaid return labels for all eligible returns within the US',
      icon: Package,
      color: 'bg-purple-100 text-purple-600'
    },
    {
      title: 'Quick Refund Processing',
      description: 'Refunds are processed within 3-5 business days of receiving your return',
      icon: CreditCard,
      color: 'bg-orange-100 text-orange-600'
    }
  ]

  const nonReturnableItems = [
    'Personalized or customized items',
    'Perishable goods',
    'Digital downloads',
    'Gift cards',
    'Items marked as final sale',
    'Underwear and intimate apparel',
    'Items damaged by misuse'
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.button
            onClick={onBackToHome}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-6"
            whileHover={{ x: -5 }}
          >
            <ArrowLeft className="h-5 w-5" />
            Back to Home
          </motion.button>
          
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Returns & Refunds</h1>
          <p className="text-lg text-gray-600">
            Easy returns and hassle-free refunds for your peace of mind
          </p>
        </motion.div>

        {/* Return Policies */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Our Return Policy</h2>
          <div className="grid md:grid-cols-2 gap-6">
            {returnPolicies.map((policy, index) => {
              const Icon = policy.icon
              return (
                <motion.div
                  key={policy.title}
                  className="bg-white rounded-xl p-6 shadow-sm border border-gray-200"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                  whileHover={{ y: -5 }}
                >
                  <div className="flex items-start gap-4">
                    <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${policy.color}`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">{policy.title}</h3>
                      <p className="text-gray-600 text-sm">{policy.description}</p>
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </motion.div>

        {/* Return Process */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6">How to Return an Item</h2>
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200">
            <div className="grid md:grid-cols-4 gap-6">
              {returnSteps.map((step, index) => {
                const Icon = step.icon
                return (
                  <motion.div
                    key={step.step}
                    className="text-center"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                  >
                    <div className="relative mb-4">
                      <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto">
                        <Icon className="h-8 w-8 text-primary-600" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {step.step}
                      </div>
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">{step.title}</h3>
                    <p className="text-sm text-gray-600">{step.description}</p>
                  </motion.div>
                )
              })}
            </div>
          </div>
        </motion.div>

        {/* Non-Returnable Items */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <div className="bg-amber-50 border border-amber-200 rounded-xl p-6">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-6 w-6 text-amber-600 flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="font-semibold text-amber-900 mb-3">Items That Cannot Be Returned</h3>
                <div className="grid md:grid-cols-2 gap-2">
                  {nonReturnableItems.map((item, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm text-amber-800">
                      <div className="w-1.5 h-1.5 bg-amber-600 rounded-full"></div>
                      {item}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Exchange Policy */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
        >
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Exchanges</h2>
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Size & Color Exchanges</h3>
                <p className="text-gray-600 mb-4">
                  Need a different size or color? We make exchanges easy! Simply select "Exchange" 
                  instead of "Return" when initiating your return.
                </p>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Same item, different size or color</li>
                  <li>• No additional shipping charges</li>
                  <li>• Faster processing than return + new order</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Different Item Exchanges</h3>
                <p className="text-gray-600 mb-4">
                  Want to exchange for a completely different item? We'll process your return 
                  and you can place a new order.
                </p>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Full refund for returned item</li>
                  <li>• Place new order separately</li>
                  <li>• Standard shipping applies to new order</li>
                </ul>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Contact Section */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Need Help with Your Return?</h3>
          <p className="text-gray-600 mb-6">
            Our customer service team is here to assist you with any return questions or issues.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.button
              className="btn-primary"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Start a Return
            </motion.button>
            <motion.button
              className="btn-secondary"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Contact Support
            </motion.button>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default ReturnsPage
