import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { VitePWA } from 'vite-plugin-pwa'

// https://vite.dev/config/
export default defineConfig({
  server: {
    port: 1001,
    host: true
  },
  plugins: [
    react(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,jpg,jpeg,gif,webp,woff,woff2,ttf,eot}']
      },
      includeAssets: ['favicon.ico', 'apple-touch-icon.png', 'eliteshop-icon.svg'],
      manifest: {
        name: 'EliteShop - Premium E-Commerce Experience',
        short_name: 'EliteShop',
        description: 'Discover premium products with AI-powered recommendations and exceptional customer service.',
        theme_color: '#6366f1',
        background_color: '#ffffff',
        display: 'standalone',
        orientation: 'portrait-primary',
        scope: '/',
        start_url: '/',
        icons: [
          {
            src: 'pwa-192x192.svg',
            sizes: '192x192',
            type: 'image/svg+xml',
            purpose: 'any maskable'
          },
          {
            src: 'pwa-512x512.svg',
            sizes: '512x512',
            type: 'image/svg+xml',
            purpose: 'any maskable'
          },
          {
            src: 'eliteshop-icon.svg',
            sizes: '32x32',
            type: 'image/svg+xml'
          }
        ],
        categories: ['shopping', 'lifestyle', 'business'],
        lang: 'en',
        dir: 'ltr'
      },
      devOptions: {
        enabled: true,
        type: 'module'
      }
    })
  ],
})
