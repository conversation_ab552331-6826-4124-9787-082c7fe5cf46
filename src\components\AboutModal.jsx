import { motion, AnimatePresence } from 'framer-motion'
import { X, Award, Users, Globe, Heart, Zap, Shield } from 'lucide-react'

const AboutModal = ({ isOpen, onClose }) => {
  if (!isOpen) return null

  const stats = [
    { icon: Users, label: 'Happy Customers', value: '50K+' },
    { icon: Globe, label: 'Countries Served', value: '25+' },
    { icon: Award, label: 'Awards Won', value: '15+' },
    { icon: Zap, label: 'Years Experience', value: '8+' }
  ]

  const values = [
    {
      icon: Heart,
      title: 'Customer First',
      description: 'Every decision we make is centered around delivering exceptional customer experiences.'
    },
    {
      icon: Shield,
      title: 'Quality Assurance',
      description: 'We rigorously test and curate every product to ensure it meets our high standards.'
    },
    {
      icon: Zap,
      title: 'Innovation',
      description: 'We stay ahead of trends to bring you the latest and greatest in technology and lifestyle.'
    },
    {
      icon: Globe,
      title: 'Sustainability',
      description: 'We are committed to responsible business practices and environmental stewardship.'
    }
  ]

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="bg-white rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto"
          initial={{ scale: 0.8, opacity: 0, y: 50 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.8, opacity: 0, y: 50 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900">About EliteShop</h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="p-6">
            {/* Hero Section */}
            <div className="text-center mb-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <h3 className="text-3xl font-bold text-gray-900 mb-4">
                  Redefining <span className="text-gradient">Premium Shopping</span>
                </h3>
                <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                  Since 2016, EliteShop has been at the forefront of curating exceptional products that enhance your lifestyle. 
                  We believe that quality, innovation, and customer satisfaction are not just goals—they're our foundation.
                </p>
              </motion.div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
              {stats.map((stat, index) => {
                const IconComponent = stat.icon
                return (
                  <motion.div
                    key={stat.label}
                    className="text-center"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                  >
                    <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mx-auto mb-3">
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>
                    <div className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</div>
                    <div className="text-gray-600 text-sm">{stat.label}</div>
                  </motion.div>
                )
              })}
            </div>

            {/* Story */}
            <div className="mb-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <h4 className="text-2xl font-bold text-gray-900 mb-6">Our Story</h4>
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <p className="text-gray-600 mb-4">
                      EliteShop was born from a simple observation: the market was flooded with products, 
                      but finding truly exceptional ones was becoming increasingly difficult. Our founders, 
                      tech enthusiasts and design lovers, set out to change that.
                    </p>
                    <p className="text-gray-600">
                      What started as a small team of five has grown into a global community of product 
                      experts, designers, and customer advocates, all united by a shared passion for excellence.
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-600 mb-4">
                      Today, we partner with leading brands and emerging innovators to bring you products 
                      that don't just meet your needs—they exceed your expectations. Every item in our 
                      catalog is carefully selected, tested, and approved by our team.
                    </p>
                    <p className="text-gray-600">
                      Our mission remains unchanged: to be your trusted guide in a world of endless choices, 
                      helping you discover products that truly make a difference in your life.
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Values */}
            <div className="mb-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <h4 className="text-2xl font-bold text-gray-900 mb-6 text-center">Our Values</h4>
                <div className="grid md:grid-cols-2 gap-6">
                  {values.map((value, index) => {
                    const IconComponent = value.icon
                    return (
                      <motion.div
                        key={value.title}
                        className="flex gap-4 p-6 bg-gray-50 rounded-xl"
                        initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                      >
                        <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                          <IconComponent className="h-6 w-6 text-primary-600" />
                        </div>
                        <div>
                          <h5 className="font-semibold text-gray-900 mb-2">{value.title}</h5>
                          <p className="text-gray-600 text-sm">{value.description}</p>
                        </div>
                      </motion.div>
                    )
                  })}
                </div>
              </motion.div>
            </div>

            {/* Team */}
            <div className="text-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                <h4 className="text-2xl font-bold text-gray-900 mb-4">Join Our Journey</h4>
                <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                  We're always looking for passionate individuals who share our vision. 
                  Whether you're a customer, partner, or potential team member, we'd love to hear from you.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <motion.button
                    className="btn-primary"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => {
                      onClose()
                      // This would typically open a careers page or contact form
                      alert('Careers page would open here')
                    }}
                  >
                    View Careers
                  </motion.button>
                  <motion.button
                    className="btn-secondary"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => {
                      onClose()
                      // This would typically open a contact form
                      alert('Contact form would open here')
                    }}
                  >
                    Get in Touch
                  </motion.button>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default AboutModal
