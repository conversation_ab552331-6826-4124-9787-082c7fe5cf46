import { useState } from 'react'
import { motion } from 'framer-motion'
import { ArrowLeft, <PERSON>ie, Settings, Eye, BarChart, Target, Shield } from 'lucide-react'

const CookiePolicyPage = ({ onBackToHome }) => {
  const [cookiePreferences, setCookiePreferences] = useState({
    essential: true, // Always required
    analytics: true,
    marketing: false,
    preferences: true
  })

  const cookieTypes = [
    {
      type: 'essential',
      title: 'Essential Cookies',
      icon: Shield,
      description: 'These cookies are necessary for the website to function and cannot be switched off.',
      required: true,
      examples: [
        'Shopping cart functionality',
        'User authentication',
        'Security features',
        'Basic site operations'
      ]
    },
    {
      type: 'analytics',
      title: 'Analytics Cookies',
      icon: BarChart,
      description: 'These cookies help us understand how visitors interact with our website.',
      required: false,
      examples: [
        'Page view tracking',
        'User behavior analysis',
        'Performance monitoring',
        'Error reporting'
      ]
    },
    {
      type: 'marketing',
      title: 'Marketing Cookies',
      icon: Target,
      description: 'These cookies track your browsing habits to show you relevant advertisements.',
      required: false,
      examples: [
        'Targeted advertising',
        'Social media integration',
        'Cross-site tracking',
        'Conversion tracking'
      ]
    },
    {
      type: 'preferences',
      title: 'Preference Cookies',
      icon: Settings,
      description: 'These cookies remember your choices and provide enhanced, personalized features.',
      required: false,
      examples: [
        'Language preferences',
        'Theme settings',
        'Layout customization',
        'Saved filters'
      ]
    }
  ]

  const handlePreferenceChange = (type) => {
    if (type === 'essential') return // Cannot be disabled
    
    setCookiePreferences(prev => ({
      ...prev,
      [type]: !prev[type]
    }))
  }

  const handleSavePreferences = () => {
    // In a real app, this would save to localStorage and update cookie consent
    console.log('Saving cookie preferences:', cookiePreferences)
    alert('Cookie preferences saved successfully!')
  }

  const handleAcceptAll = () => {
    setCookiePreferences({
      essential: true,
      analytics: true,
      marketing: true,
      preferences: true
    })
  }

  const handleRejectAll = () => {
    setCookiePreferences({
      essential: true, // Cannot be disabled
      analytics: false,
      marketing: false,
      preferences: false
    })
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.button
            onClick={onBackToHome}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-6"
            whileHover={{ x: -5 }}
          >
            <ArrowLeft className="h-5 w-5" />
            Back to Home
          </motion.button>
          
          <div className="flex items-center gap-4 mb-4">
            <Cookie className="h-10 w-10 text-primary-600" />
            <h1 className="text-4xl font-bold text-gray-900">Cookie Policy</h1>
          </div>
          <p className="text-lg text-gray-600 mb-4">
            Learn about how we use cookies and manage your preferences.
          </p>
          <p className="text-sm text-gray-500">
            Last updated: January 2024
          </p>
        </motion.div>

        {/* Introduction */}
        <motion.div
          className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-4">What Are Cookies?</h2>
          <p className="text-gray-600 mb-4">
            Cookies are small text files that are stored on your device when you visit our website. 
            They help us provide you with a better browsing experience by remembering your preferences 
            and understanding how you use our site.
          </p>
          <p className="text-gray-600">
            We use different types of cookies for various purposes, and you have control over which 
            ones you want to accept. Essential cookies are required for the site to function properly, 
            while others enhance your experience or help us improve our services.
          </p>
        </motion.div>

        {/* Cookie Types and Preferences */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Cookie Preferences</h2>
          <div className="space-y-6">
            {cookieTypes.map((cookie, index) => {
              const Icon = cookie.icon
              const isEnabled = cookiePreferences[cookie.type]
              
              return (
                <motion.div
                  key={cookie.type}
                  className="bg-white rounded-xl p-6 shadow-sm border border-gray-200"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-4 flex-1">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                        isEnabled ? 'bg-primary-100' : 'bg-gray-100'
                      }`}>
                        <Icon className={`h-6 w-6 ${
                          isEnabled ? 'text-primary-600' : 'text-gray-400'
                        }`} />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{cookie.title}</h3>
                          {cookie.required && (
                            <span className="px-2 py-1 bg-red-100 text-red-700 text-xs font-medium rounded-full">
                              Required
                            </span>
                          )}
                        </div>
                        <p className="text-gray-600 mb-4">{cookie.description}</p>
                        
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Examples:</h4>
                          <ul className="space-y-1">
                            {cookie.examples.map((example, idx) => (
                              <li key={idx} className="flex items-center gap-2 text-sm text-gray-600">
                                <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                                {example}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                    
                    <div className="ml-4">
                      <motion.button
                        onClick={() => handlePreferenceChange(cookie.type)}
                        disabled={cookie.required}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          isEnabled ? 'bg-primary-500' : 'bg-gray-300'
                        } ${cookie.required ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                        whileTap={{ scale: 0.95 }}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            isEnabled ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </motion.button>
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </motion.div>

        {/* Preference Controls */}
        <motion.div
          className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Manage Your Preferences</h2>
          <div className="flex flex-col sm:flex-row gap-4">
            <motion.button
              onClick={handleAcceptAll}
              className="btn-primary flex-1"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Accept All Cookies
            </motion.button>
            <motion.button
              onClick={handleRejectAll}
              className="btn-secondary flex-1"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Reject Non-Essential
            </motion.button>
            <motion.button
              onClick={handleSavePreferences}
              className="bg-green-600 text-white px-6 py-3 rounded-xl font-medium hover:bg-green-700 transition-colors flex-1"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Save Preferences
            </motion.button>
          </div>
        </motion.div>

        {/* Additional Information */}
        <motion.div
          className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Additional Information</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Third-Party Cookies</h3>
              <p className="text-gray-600 text-sm mb-4">
                Some cookies are set by third-party services that appear on our pages. 
                These include analytics providers, advertising networks, and social media platforms.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Browser Settings</h3>
              <p className="text-gray-600 text-sm mb-4">
                You can also control cookies through your browser settings. However, 
                disabling certain cookies may affect the functionality of our website.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Cookie Duration</h3>
              <p className="text-gray-600 text-sm mb-4">
                Some cookies are deleted when you close your browser (session cookies), 
                while others remain on your device for a set period (persistent cookies).
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Updates</h3>
              <p className="text-gray-600 text-sm mb-4">
                We may update this cookie policy from time to time. 
                We'll notify you of any significant changes through our website.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Contact Section */}
        <motion.div
          className="bg-primary-50 rounded-2xl p-8 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.2 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Questions About Cookies?</h2>
          <p className="text-gray-600 mb-6">
            If you have any questions about our use of cookies, please don't hesitate to contact us.
          </p>
          <motion.button
            className="btn-primary"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Contact Privacy Team
          </motion.button>
          
          <div className="mt-6 pt-6 border-t border-primary-200">
            <p className="text-sm text-gray-500">
              Email: <EMAIL> | Phone: +****************
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default CookiePolicyPage
